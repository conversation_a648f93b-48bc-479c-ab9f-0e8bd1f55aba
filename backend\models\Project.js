import mongoose from 'mongoose';
import { PROJECT_CATEGORIES, PROJECT_STATUS, TECH_STACKS } from '../utils/constants.js';
import { generateSlug } from '../utils/helpers.js';

const projectSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Project title is required'],
    trim: true,
    maxlength: [100, 'Title cannot exceed 100 characters']
  },
  slug: {
    type: String,
    lowercase: true
  },
  description: {
    type: String,
    required: [true, 'Project description is required'],
    trim: true,
    maxlength: [2000, 'Description cannot exceed 2000 characters']
  },
  shortDescription: {
    type: String,
    maxlength: [200, 'Short description cannot exceed 200 characters']
  },
  category: {
    type: String,
    required: [true, 'Project category is required'],
    enum: Object.values(PROJECT_CATEGORIES)
  },
  techStack: [{
    type: String,
    enum: Object.values(TECH_STACKS),
    required: true
  }],
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  price: {
    type: Number,
    required: [true, 'Price is required'],
    min: [0, 'Price cannot be negative'],
    max: [10000, 'Price cannot exceed $10,000']
  },
  // File storage
  files: {
    // GitHub repository
    githubUrl: {
      type: String,
      match: [/^https:\/\/github\.com\/[^\/]+\/[^\/]+$/, 'Please provide a valid GitHub repository URL']
    },
    // ZIP file upload
    zipFile: {
      public_id: String,
      url: String,
      filename: String,
      size: Number
    },
    // Preview images
    previewImages: [{
      public_id: String,
      url: String,
      caption: String
    }],
    // Demo/live preview URL
    demoUrl: {
      type: String,
      match: [/^https?:\/\/.+/, 'Please provide a valid demo URL']
    }
  },
  // Project details
  features: [{
    type: String,
    trim: true
  }],
  requirements: [{
    type: String,
    trim: true
  }],
  installation: {
    type: String,
    trim: true
  },
  usage: {
    type: String,
    trim: true
  },
  // Seller information
  uploadedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  // Status and moderation
  status: {
    type: String,
    enum: Object.values(PROJECT_STATUS),
    default: PROJECT_STATUS.PENDING
  },
  rejectionReason: String,
  // Analytics
  views: {
    type: Number,
    default: 0
  },
  downloads: {
    type: Number,
    default: 0
  },
  sales: {
    type: Number,
    default: 0
  },
  revenue: {
    type: Number,
    default: 0
  },
  // Ratings and reviews
  averageRating: {
    type: Number,
    default: 0,
    min: 0,
    max: 5
  },
  totalReviews: {
    type: Number,
    default: 0
  },
  // AI Analysis results
  aiAnalysis: {
    qualityScore: {
      type: Number,
      min: 0,
      max: 100
    },
    complexityLevel: {
      type: String,
      enum: ['beginner', 'intermediate', 'advanced']
    },
    suggestedTags: [String],
    suggestedImprovements: [String],
    detectedTechnologies: [String],
    codeQualityMetrics: {
      maintainability: Number,
      readability: Number,
      testCoverage: Number,
      documentation: Number
    },
    estimatedPopularity: {
      type: Number,
      min: 0,
      max: 100
    },
    lastAnalyzed: Date
  },
  // SEO and discovery
  searchKeywords: [String],
  isActive: {
    type: Boolean,
    default: true
  },
  isFeatured: {
    type: Boolean,
    default: false
  },
  // Timestamps
  publishedAt: Date,
  lastUpdated: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
projectSchema.index({ title: 'text', description: 'text', tags: 'text' });
projectSchema.index({ category: 1 });
projectSchema.index({ techStack: 1 });
projectSchema.index({ price: 1 });
projectSchema.index({ averageRating: -1 });
projectSchema.index({ sales: -1 });
projectSchema.index({ createdAt: -1 });
projectSchema.index({ status: 1, isActive: 1 });
projectSchema.index({ uploadedBy: 1 });
projectSchema.index({ slug: 1 });

// Virtual for reviews
projectSchema.virtual('reviews', {
  ref: 'Review',
  localField: '_id',
  foreignField: 'project'
});

// Virtual for seller info
projectSchema.virtual('seller', {
  ref: 'User',
  localField: 'uploadedBy',
  foreignField: '_id',
  justOne: true
});

// Pre-save middleware to generate slug
projectSchema.pre('save', function(next) {
  if (this.isModified('title')) {
    this.slug = generateSlug(this.title);
    
    // Ensure slug uniqueness
    const randomSuffix = Math.random().toString(36).substring(2, 8);
    this.slug = `${this.slug}-${randomSuffix}`;
  }
  
  // Generate short description if not provided
  if (!this.shortDescription && this.description) {
    this.shortDescription = this.description.substring(0, 150) + '...';
  }
  
  // Set published date when status changes to approved
  if (this.isModified('status') && this.status === PROJECT_STATUS.APPROVED && !this.publishedAt) {
    this.publishedAt = new Date();
  }
  
  next();
});

// Static method to get trending projects
projectSchema.statics.getTrending = function(limit = 10) {
  return this.find({
    status: PROJECT_STATUS.APPROVED,
    isActive: true
  })
  .sort({ sales: -1, views: -1, createdAt: -1 })
  .limit(limit)
  .populate('uploadedBy', 'name avatar sellerRating')
  .select('-aiAnalysis -files.zipFile');
};

// Static method to get featured projects
projectSchema.statics.getFeatured = function(limit = 6) {
  return this.find({
    status: PROJECT_STATUS.APPROVED,
    isActive: true,
    isFeatured: true
  })
  .sort({ createdAt: -1 })
  .limit(limit)
  .populate('uploadedBy', 'name avatar sellerRating')
  .select('-aiAnalysis -files.zipFile');
};

// Static method for advanced search
projectSchema.statics.advancedSearch = function(filters, options = {}) {
  const {
    query,
    category,
    techStack,
    minPrice,
    maxPrice,
    minRating,
    tags,
    sortBy = 'createdAt',
    sortOrder = 'desc',
    page = 1,
    limit = 12
  } = filters;

  let searchQuery = {
    status: PROJECT_STATUS.APPROVED,
    isActive: true
  };

  // Text search
  if (query) {
    searchQuery.$text = { $search: query };
  }

  // Category filter
  if (category) {
    searchQuery.category = category;
  }

  // Tech stack filter
  if (techStack && techStack.length > 0) {
    searchQuery.techStack = { $in: techStack };
  }

  // Price range filter
  if (minPrice !== undefined || maxPrice !== undefined) {
    searchQuery.price = {};
    if (minPrice !== undefined) searchQuery.price.$gte = minPrice;
    if (maxPrice !== undefined) searchQuery.price.$lte = maxPrice;
  }

  // Rating filter
  if (minRating) {
    searchQuery.averageRating = { $gte: minRating };
  }

  // Tags filter
  if (tags && tags.length > 0) {
    searchQuery.tags = { $in: tags };
  }

  // Sorting
  const sortOptions = {};
  sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

  // Pagination
  const skip = (page - 1) * limit;

  return this.find(searchQuery)
    .sort(sortOptions)
    .skip(skip)
    .limit(limit)
    .populate('uploadedBy', 'name avatar sellerRating')
    .select('-aiAnalysis -files.zipFile');
};

// Instance method to increment views
projectSchema.methods.incrementViews = function() {
  return this.updateOne({ $inc: { views: 1 } });
};

// Instance method to increment downloads
projectSchema.methods.incrementDownloads = function() {
  return this.updateOne({ $inc: { downloads: 1 } });
};

// Instance method to update rating
projectSchema.methods.updateRating = async function() {
  const Review = mongoose.model('Review');
  const stats = await Review.aggregate([
    { $match: { project: this._id } },
    {
      $group: {
        _id: null,
        averageRating: { $avg: '$rating' },
        totalReviews: { $sum: 1 }
      }
    }
  ]);

  if (stats.length > 0) {
    this.averageRating = Math.round(stats[0].averageRating * 10) / 10;
    this.totalReviews = stats[0].totalReviews;
  } else {
    this.averageRating = 0;
    this.totalReviews = 0;
  }

  return this.save();
};

export default mongoose.model('Project', projectSchema);
