import Project from '../models/Project.js';
import User from '../models/User.js';
import { asyncHandler } from '../middleware/errorHandler.js';
import { uploadZipToCloudinary, uploadImageToCloudinary, deleteFromCloudinary } from '../config/cloudinary.js';
import { PROJECT_STATUS, SUCCESS_MESSAGES, PAGINATION_DEFAULTS } from '../utils/constants.js';
import { formatPaginationResponse, isValidGitHubUrl, extractGitHubInfo } from '../utils/helpers.js';

// @desc    Create a new project
// @route   POST /api/projects
// @access  Private (Seller only)
export const createProject = asyncHandler(async (req, res) => {
  const {
    title,
    description,
    category,
    techStack,
    price,
    tags,
    githubUrl,
    demoUrl,
    features,
    requirements,
    installation,
    usage
  } = req.body;

  // Validate GitHub URL if provided
  if (githubUrl && !isValidGitHubUrl(githubUrl)) {
    return res.status(400).json({
      success: false,
      error: 'Please provide a valid GitHub repository URL'
    });
  }

  try {
    // Create project object
    const projectData = {
      title,
      description,
      category,
      techStack,
      price,
      tags: tags || [],
      features: features || [],
      requirements: requirements || [],
      installation,
      usage,
      uploadedBy: req.user._id,
      files: {
        githubUrl,
        demoUrl
      }
    };

    // Handle file uploads
    if (req.files) {
      // Handle ZIP file upload
      if (req.files.projectZip && req.files.projectZip[0]) {
        const zipFile = req.files.projectZip[0];
        const zipUpload = await uploadZipToCloudinary(zipFile.path, 'temp');
        
        projectData.files.zipFile = {
          public_id: zipUpload.public_id,
          url: zipUpload.url,
          filename: zipFile.originalname,
          size: zipFile.size
        };
      }

      // Handle preview images upload
      if (req.files.previewImages && req.files.previewImages.length > 0) {
        const imageUploads = await Promise.all(
          req.files.previewImages.map(async (image) => {
            const imageUpload = await uploadImageToCloudinary(image.path, 'temp');
            return {
              public_id: imageUpload.public_id,
              url: imageUpload.url,
              caption: image.originalname
            };
          })
        );
        
        projectData.files.previewImages = imageUploads;
      }
    }

    // Create project
    const project = await Project.create(projectData);

    // Update project files with actual project ID
    if (project.files.zipFile || project.files.previewImages.length > 0) {
      // Re-upload files with proper project ID folder structure
      if (project.files.zipFile) {
        const newZipUpload = await uploadZipToCloudinary(
          project.files.zipFile.url, 
          project._id
        );
        project.files.zipFile = {
          ...project.files.zipFile,
          public_id: newZipUpload.public_id,
          url: newZipUpload.url
        };
      }

      if (project.files.previewImages.length > 0) {
        const newImageUploads = await Promise.all(
          project.files.previewImages.map(async (image) => {
            const newImageUpload = await uploadImageToCloudinary(
              image.url,
              project._id
            );
            return {
              ...image,
              public_id: newImageUpload.public_id,
              url: newImageUpload.url
            };
          })
        );
        project.files.previewImages = newImageUploads;
      }

      await project.save();
    }

    // Populate seller information
    await project.populate('uploadedBy', 'name avatar sellerRating');

    res.status(201).json({
      success: true,
      message: SUCCESS_MESSAGES.PROJECT_CREATED,
      data: {
        project
      }
    });
  } catch (error) {
    console.error('Project creation error:', error);
    res.status(400).json({
      success: false,
      error: error.message || 'Project creation failed'
    });
  }
});

// @desc    Get all projects with filtering and pagination
// @route   GET /api/projects
// @access  Public
export const getProjects = asyncHandler(async (req, res) => {
  const {
    page = PAGINATION_DEFAULTS.PAGE,
    limit = PAGINATION_DEFAULTS.LIMIT,
    category,
    techStack,
    minPrice,
    maxPrice,
    minRating,
    tags,
    search,
    sortBy = 'createdAt',
    sortOrder = 'desc',
    featured,
    trending
  } = req.query;

  // Handle special cases
  if (featured === 'true') {
    const featuredProjects = await Project.getFeatured(parseInt(limit));
    return res.status(200).json({
      success: true,
      data: {
        projects: featuredProjects,
        pagination: {
          currentPage: 1,
          totalPages: 1,
          totalItems: featuredProjects.length,
          itemsPerPage: featuredProjects.length
        }
      }
    });
  }

  if (trending === 'true') {
    const trendingProjects = await Project.getTrending(parseInt(limit));
    return res.status(200).json({
      success: true,
      data: {
        projects: trendingProjects,
        pagination: {
          currentPage: 1,
          totalPages: 1,
          totalItems: trendingProjects.length,
          itemsPerPage: trendingProjects.length
        }
      }
    });
  }

  // Build filter object
  const filters = {
    query: search,
    category,
    techStack: techStack ? (Array.isArray(techStack) ? techStack : [techStack]) : undefined,
    minPrice: minPrice ? parseFloat(minPrice) : undefined,
    maxPrice: maxPrice ? parseFloat(maxPrice) : undefined,
    minRating: minRating ? parseFloat(minRating) : undefined,
    tags: tags ? (Array.isArray(tags) ? tags : [tags]) : undefined,
    sortBy,
    sortOrder,
    page: parseInt(page),
    limit: parseInt(limit)
  };

  // Get projects using advanced search
  const projects = await Project.advancedSearch(filters);

  // Get total count for pagination
  let countQuery = {
    status: PROJECT_STATUS.APPROVED,
    isActive: true
  };

  if (search) countQuery.$text = { $search: search };
  if (category) countQuery.category = category;
  if (techStack) countQuery.techStack = { $in: Array.isArray(techStack) ? techStack : [techStack] };
  if (minPrice !== undefined || maxPrice !== undefined) {
    countQuery.price = {};
    if (minPrice !== undefined) countQuery.price.$gte = parseFloat(minPrice);
    if (maxPrice !== undefined) countQuery.price.$lte = parseFloat(maxPrice);
  }
  if (minRating) countQuery.averageRating = { $gte: parseFloat(minRating) };
  if (tags) countQuery.tags = { $in: Array.isArray(tags) ? tags : [tags] };

  const totalProjects = await Project.countDocuments(countQuery);

  const response = formatPaginationResponse(
    projects,
    totalProjects,
    parseInt(page),
    parseInt(limit)
  );

  res.status(200).json({
    success: true,
    data: response
  });
});

// @desc    Get single project by ID or slug
// @route   GET /api/projects/:id
// @access  Public
export const getProject = asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Try to find by ID first, then by slug
  let project = await Project.findById(id)
    .populate('uploadedBy', 'name avatar bio sellerRating totalSales createdAt')
    .populate('reviews');

  if (!project) {
    project = await Project.findOne({ slug: id })
      .populate('uploadedBy', 'name avatar bio sellerRating totalSales createdAt')
      .populate('reviews');
  }

  if (!project) {
    return res.status(404).json({
      success: false,
      error: 'Project not found'
    });
  }

  // Check if project is accessible
  if (project.status !== PROJECT_STATUS.APPROVED && 
      project.uploadedBy._id.toString() !== req.user?._id.toString()) {
    return res.status(404).json({
      success: false,
      error: 'Project not found'
    });
  }

  // Increment view count (only for approved projects)
  if (project.status === PROJECT_STATUS.APPROVED) {
    await project.incrementViews();
  }

  res.status(200).json({
    success: true,
    data: {
      project
    }
  });
});

// @desc    Update project
// @route   PUT /api/projects/:id
// @access  Private (Owner only)
export const updateProject = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const updateData = { ...req.body };

  const project = await Project.findById(id);

  if (!project) {
    return res.status(404).json({
      success: false,
      error: 'Project not found'
    });
  }

  // Check ownership
  if (project.uploadedBy.toString() !== req.user._id.toString()) {
    return res.status(403).json({
      success: false,
      error: 'Access denied. You can only update your own projects.'
    });
  }

  // Validate GitHub URL if provided
  if (updateData.githubUrl && !isValidGitHubUrl(updateData.githubUrl)) {
    return res.status(400).json({
      success: false,
      error: 'Please provide a valid GitHub repository URL'
    });
  }

  // Handle file updates
  if (req.files) {
    // Handle new ZIP file upload
    if (req.files.projectZip && req.files.projectZip[0]) {
      // Delete old ZIP file if exists
      if (project.files.zipFile && project.files.zipFile.public_id) {
        await deleteFromCloudinary(project.files.zipFile.public_id, 'raw');
      }

      const zipFile = req.files.projectZip[0];
      const zipUpload = await uploadZipToCloudinary(zipFile.path, project._id);
      
      updateData.files = {
        ...project.files,
        zipFile: {
          public_id: zipUpload.public_id,
          url: zipUpload.url,
          filename: zipFile.originalname,
          size: zipFile.size
        }
      };
    }

    // Handle new preview images
    if (req.files.previewImages && req.files.previewImages.length > 0) {
      // Delete old preview images
      if (project.files.previewImages && project.files.previewImages.length > 0) {
        await Promise.all(
          project.files.previewImages.map(image => 
            deleteFromCloudinary(image.public_id, 'image')
          )
        );
      }

      const imageUploads = await Promise.all(
        req.files.previewImages.map(async (image) => {
          const imageUpload = await uploadImageToCloudinary(image.path, project._id);
          return {
            public_id: imageUpload.public_id,
            url: imageUpload.url,
            caption: image.originalname
          };
        })
      );
      
      updateData.files = {
        ...updateData.files || project.files,
        previewImages: imageUploads
      };
    }
  }

  // Update last modified timestamp
  updateData.lastUpdated = new Date();

  // If project was rejected and now being updated, reset status to pending
  if (project.status === PROJECT_STATUS.REJECTED) {
    updateData.status = PROJECT_STATUS.PENDING;
    updateData.rejectionReason = undefined;
  }

  const updatedProject = await Project.findByIdAndUpdate(
    id,
    updateData,
    {
      new: true,
      runValidators: true
    }
  ).populate('uploadedBy', 'name avatar sellerRating');

  res.status(200).json({
    success: true,
    message: SUCCESS_MESSAGES.PROJECT_UPDATED,
    data: {
      project: updatedProject
    }
  });
});

// @desc    Delete project
// @route   DELETE /api/projects/:id
// @access  Private (Owner only)
export const deleteProject = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const project = await Project.findById(id);

  if (!project) {
    return res.status(404).json({
      success: false,
      error: 'Project not found'
    });
  }

  // Check ownership
  if (project.uploadedBy.toString() !== req.user._id.toString()) {
    return res.status(403).json({
      success: false,
      error: 'Access denied. You can only delete your own projects.'
    });
  }

  // Delete associated files from Cloudinary
  try {
    if (project.files.zipFile && project.files.zipFile.public_id) {
      await deleteFromCloudinary(project.files.zipFile.public_id, 'raw');
    }

    if (project.files.previewImages && project.files.previewImages.length > 0) {
      await Promise.all(
        project.files.previewImages.map(image =>
          deleteFromCloudinary(image.public_id, 'image')
        )
      );
    }
  } catch (error) {
    console.error('Error deleting files from Cloudinary:', error);
  }

  // Delete project
  await Project.findByIdAndDelete(id);

  res.status(200).json({
    success: true,
    message: SUCCESS_MESSAGES.PROJECT_DELETED
  });
});

// @desc    Get seller's projects
// @route   GET /api/projects/seller/:sellerId
// @access  Public
export const getSellerProjects = asyncHandler(async (req, res) => {
  const { sellerId } = req.params;
  const {
    page = PAGINATION_DEFAULTS.PAGE,
    limit = PAGINATION_DEFAULTS.LIMIT,
    status
  } = req.query;

  // Build query
  let query = { uploadedBy: sellerId };

  // If not the owner, only show approved projects
  if (!req.user || req.user._id.toString() !== sellerId) {
    query.status = PROJECT_STATUS.APPROVED;
    query.isActive = true;
  } else if (status) {
    query.status = status;
  }

  const skip = (parseInt(page) - 1) * parseInt(limit);

  const projects = await Project.find(query)
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(parseInt(limit))
    .populate('uploadedBy', 'name avatar sellerRating')
    .select('-files.zipFile'); // Don't include download links

  const totalProjects = await Project.countDocuments(query);

  const response = formatPaginationResponse(
    projects,
    totalProjects,
    parseInt(page),
    parseInt(limit)
  );

  res.status(200).json({
    success: true,
    data: response
  });
});

// @desc    Get my projects (for seller dashboard)
// @route   GET /api/projects/my
// @access  Private (Seller only)
export const getMyProjects = asyncHandler(async (req, res) => {
  const {
    page = PAGINATION_DEFAULTS.PAGE,
    limit = PAGINATION_DEFAULTS.LIMIT,
    status
  } = req.query;

  let query = { uploadedBy: req.user._id };

  if (status) {
    query.status = status;
  }

  const skip = (parseInt(page) - 1) * parseInt(limit);

  const projects = await Project.find(query)
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(parseInt(limit))
    .select('-files.zipFile'); // Don't include download links in list

  const totalProjects = await Project.countDocuments(query);

  // Get status counts for dashboard
  const statusCounts = await Project.aggregate([
    { $match: { uploadedBy: req.user._id } },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 }
      }
    }
  ]);

  const statusSummary = statusCounts.reduce((acc, item) => {
    acc[item._id] = item.count;
    return acc;
  }, {});

  const response = formatPaginationResponse(
    projects,
    totalProjects,
    parseInt(page),
    parseInt(limit)
  );

  res.status(200).json({
    success: true,
    data: {
      ...response,
      statusSummary
    }
  });
});

// @desc    Download project files
// @route   GET /api/projects/:id/download
// @access  Private (Purchased users only)
export const downloadProject = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const project = await Project.findById(id);

  if (!project) {
    return res.status(404).json({
      success: false,
      error: 'Project not found'
    });
  }

  // Check if user has purchased this project or is the owner
  const Transaction = (await import('../models/Transaction.js')).default;

  const hasPurchased = await Transaction.findOne({
    project: id,
    buyer: req.user._id,
    status: 'completed'
  });

  const isOwner = project.uploadedBy.toString() === req.user._id.toString();

  if (!hasPurchased && !isOwner) {
    return res.status(403).json({
      success: false,
      error: 'Access denied. You must purchase this project to download it.'
    });
  }

  // Check download limit for purchased users
  if (hasPurchased && !hasPurchased.canDownload) {
    return res.status(403).json({
      success: false,
      error: 'Download limit exceeded for this project.'
    });
  }

  // Get download URL
  let downloadUrl = null;

  if (project.files.zipFile && project.files.zipFile.url) {
    downloadUrl = project.files.zipFile.url;
  } else if (project.files.githubUrl) {
    downloadUrl = `${project.files.githubUrl}/archive/refs/heads/main.zip`;
  }

  if (!downloadUrl) {
    return res.status(404).json({
      success: false,
      error: 'No downloadable files available for this project.'
    });
  }

  // Track download
  if (hasPurchased) {
    await hasPurchased.trackDownload();
  }

  await project.incrementDownloads();

  res.status(200).json({
    success: true,
    data: {
      downloadUrl,
      filename: project.files.zipFile?.filename || `${project.title}.zip`,
      remainingDownloads: hasPurchased ? (hasPurchased.downloadLimit - hasPurchased.downloadCount - 1) : 'unlimited'
    }
  });
});
