import OpenAI from 'openai';
import { TECH_STACKS, PROJECT_CATEGORIES } from '../utils/constants.js';

// Initialize OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Analyze project and generate AI insights
export const analyzeProject = async (projectData, githubAnalysis = null) => {
  try {
    const analysis = {
      qualityScore: 0,
      complexityLevel: 'beginner',
      suggestedTags: [],
      suggestedImprovements: [],
      detectedTechnologies: [],
      codeQualityMetrics: {
        maintainability: 0,
        readability: 0,
        testCoverage: 0,
        documentation: 0
      },
      estimatedPopularity: 0,
      lastAnalyzed: new Date()
    };

    // Prepare context for AI analysis
    const context = prepareAnalysisContext(projectData, githubAnalysis);
    
    // Run AI analysis
    const aiInsights = await generateAIInsights(context);
    
    // Process AI response
    if (aiInsights) {
      analysis.qualityScore = aiInsights.qualityScore || calculateBasicQualityScore(projectData, githubAnalysis);
      analysis.complexityLevel = aiInsights.complexityLevel || determineComplexityLevel(projectData, githubAnalysis);
      analysis.suggestedTags = aiInsights.suggestedTags || generateBasicTags(projectData, githubAnalysis);
      analysis.suggestedImprovements = aiInsights.improvements || [];
      analysis.detectedTechnologies = aiInsights.technologies || extractTechnologies(projectData, githubAnalysis);
      analysis.codeQualityMetrics = aiInsights.metrics || calculateBasicMetrics(projectData, githubAnalysis);
      analysis.estimatedPopularity = aiInsights.popularity || estimateBasicPopularity(projectData, githubAnalysis);
    } else {
      // Fallback to basic analysis if AI fails
      analysis.qualityScore = calculateBasicQualityScore(projectData, githubAnalysis);
      analysis.complexityLevel = determineComplexityLevel(projectData, githubAnalysis);
      analysis.suggestedTags = generateBasicTags(projectData, githubAnalysis);
      analysis.detectedTechnologies = extractTechnologies(projectData, githubAnalysis);
      analysis.codeQualityMetrics = calculateBasicMetrics(projectData, githubAnalysis);
      analysis.estimatedPopularity = estimateBasicPopularity(projectData, githubAnalysis);
    }

    return analysis;
  } catch (error) {
    console.error('AI Analysis error:', error);
    
    // Return basic analysis as fallback
    return {
      qualityScore: calculateBasicQualityScore(projectData, githubAnalysis),
      complexityLevel: determineComplexityLevel(projectData, githubAnalysis),
      suggestedTags: generateBasicTags(projectData, githubAnalysis),
      suggestedImprovements: ['Consider adding more detailed documentation', 'Add unit tests for better code quality'],
      detectedTechnologies: extractTechnologies(projectData, githubAnalysis),
      codeQualityMetrics: calculateBasicMetrics(projectData, githubAnalysis),
      estimatedPopularity: estimateBasicPopularity(projectData, githubAnalysis),
      lastAnalyzed: new Date()
    };
  }
};

// Prepare context for AI analysis
const prepareAnalysisContext = (projectData, githubAnalysis) => {
  const context = {
    title: projectData.title,
    description: projectData.description,
    category: projectData.category,
    techStack: projectData.techStack,
    price: projectData.price,
    features: projectData.features || [],
    requirements: projectData.requirements || []
  };

  if (githubAnalysis) {
    context.github = {
      technologies: githubAnalysis.technologies,
      structure: githubAnalysis.structure,
      complexity: githubAnalysis.complexity,
      quality: githubAnalysis.quality,
      metadata: githubAnalysis.metadata
    };
  }

  return context;
};

// Generate AI insights using OpenAI
const generateAIInsights = async (context) => {
  try {
    const prompt = `
Analyze this web development project and provide insights in JSON format:

Project Details:
- Title: ${context.title}
- Description: ${context.description}
- Category: ${context.category}
- Tech Stack: ${context.techStack?.join(', ')}
- Price: $${context.price}
- Features: ${context.features?.join(', ')}

${context.github ? `
GitHub Analysis:
- Technologies: ${context.github.technologies?.detected?.join(', ')}
- Stars: ${context.github.metadata?.stars}
- Forks: ${context.github.metadata?.forks}
- Last Update: ${context.github.metadata?.lastUpdate}
- Quality Metrics: ${JSON.stringify(context.github.quality)}
` : ''}

Please provide analysis in this exact JSON format:
{
  "qualityScore": number (0-100),
  "complexityLevel": "beginner" | "intermediate" | "advanced",
  "suggestedTags": [array of relevant tags],
  "improvements": [array of improvement suggestions],
  "technologies": [array of detected technologies],
  "metrics": {
    "maintainability": number (0-100),
    "readability": number (0-100),
    "testCoverage": number (0-100),
    "documentation": number (0-100)
  },
  "popularity": number (0-100)
}

Consider factors like:
- Code quality and structure
- Documentation completeness
- Technology choices
- Market demand
- Pricing appropriateness
- Feature completeness
`;

    const response = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: "You are an expert web development project analyst. Provide accurate, helpful analysis in valid JSON format only."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      max_tokens: 1000,
      temperature: 0.3
    });

    const content = response.choices[0]?.message?.content;
    if (content) {
      // Extract JSON from response
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
    }

    return null;
  } catch (error) {
    console.error('OpenAI API error:', error);
    return null;
  }
};

// Fallback functions for basic analysis

const calculateBasicQualityScore = (projectData, githubAnalysis) => {
  let score = 50; // Base score

  // Project completeness
  if (projectData.description && projectData.description.length > 100) score += 10;
  if (projectData.features && projectData.features.length > 0) score += 10;
  if (projectData.requirements && projectData.requirements.length > 0) score += 5;
  if (projectData.techStack && projectData.techStack.length > 0) score += 10;

  // GitHub factors
  if (githubAnalysis) {
    score += Math.min(githubAnalysis.metadata.stars * 2, 15);
    if (githubAnalysis.structure.hasTests) score += 10;
    if (githubAnalysis.structure.hasDocumentation) score += 5;
    score += Math.min(githubAnalysis.quality.documentation / 5, 10);
  }

  return Math.min(score, 100);
};

const determineComplexityLevel = (projectData, githubAnalysis) => {
  let complexityPoints = 0;

  // Tech stack complexity
  if (projectData.techStack) {
    complexityPoints += projectData.techStack.length * 2;
    
    // Advanced technologies
    const advancedTechs = ['Docker', 'Kubernetes', 'GraphQL', 'TypeScript', 'Next.js', 'NestJS'];
    const hasAdvanced = projectData.techStack.some(tech => advancedTechs.includes(tech));
    if (hasAdvanced) complexityPoints += 10;
  }

  // GitHub complexity
  if (githubAnalysis) {
    complexityPoints += githubAnalysis.complexity / 5;
    if (githubAnalysis.technologies.detected.length > 5) complexityPoints += 5;
  }

  // Price indicator
  if (projectData.price > 100) complexityPoints += 10;
  else if (projectData.price > 50) complexityPoints += 5;

  if (complexityPoints >= 25) return 'advanced';
  if (complexityPoints >= 15) return 'intermediate';
  return 'beginner';
};

const generateBasicTags = (projectData, githubAnalysis) => {
  const tags = new Set();

  // Add tech stack as tags
  if (projectData.techStack) {
    projectData.techStack.forEach(tech => tags.add(tech.toLowerCase()));
  }

  // Add category-based tags
  if (projectData.category) {
    tags.add(projectData.category);
    
    // Add related tags based on category
    const categoryTags = {
      'frontend': ['ui', 'responsive', 'web'],
      'backend': ['api', 'server', 'database'],
      'fullstack': ['web-app', 'complete', 'end-to-end'],
      'mobile': ['app', 'responsive'],
      'template': ['starter', 'boilerplate'],
      'component': ['reusable', 'ui-component']
    };
    
    if (categoryTags[projectData.category]) {
      categoryTags[projectData.category].forEach(tag => tags.add(tag));
    }
  }

  // Add GitHub-based tags
  if (githubAnalysis) {
    githubAnalysis.technologies.detected.forEach(tech => {
      tags.add(tech.toLowerCase().replace(/\s+/g, '-'));
    });
  }

  return Array.from(tags).slice(0, 10);
};

const extractTechnologies = (projectData, githubAnalysis) => {
  const technologies = new Set();

  if (projectData.techStack) {
    projectData.techStack.forEach(tech => technologies.add(tech));
  }

  if (githubAnalysis) {
    githubAnalysis.technologies.detected.forEach(tech => technologies.add(tech));
  }

  return Array.from(technologies);
};

const calculateBasicMetrics = (projectData, githubAnalysis) => {
  const metrics = {
    maintainability: 50,
    readability: 50,
    testCoverage: 0,
    documentation: 30
  };

  if (githubAnalysis) {
    metrics.maintainability = githubAnalysis.quality.maintainability || 50;
    metrics.documentation = githubAnalysis.quality.documentation || 30;
    
    if (githubAnalysis.structure.hasTests) {
      metrics.testCoverage = 60;
    }
  }

  // Boost documentation score if project has good description
  if (projectData.description && projectData.description.length > 200) {
    metrics.documentation += 20;
  }

  if (projectData.features && projectData.features.length > 3) {
    metrics.documentation += 10;
  }

  return metrics;
};

const estimateBasicPopularity = (projectData, githubAnalysis) => {
  let popularity = 30; // Base score

  // Category popularity
  const popularCategories = ['frontend', 'fullstack', 'template', 'component'];
  if (popularCategories.includes(projectData.category)) {
    popularity += 20;
  }

  // Tech stack popularity
  const popularTechs = ['React', 'Next.js', 'Vue.js', 'Node.js', 'TypeScript', 'Tailwind CSS'];
  if (projectData.techStack) {
    const hasPopularTech = projectData.techStack.some(tech => popularTechs.includes(tech));
    if (hasPopularTech) popularity += 15;
  }

  // GitHub popularity
  if (githubAnalysis) {
    popularity += Math.min(githubAnalysis.metadata.stars, 30);
    popularity += Math.min(githubAnalysis.metadata.forks * 2, 15);
  }

  // Price factor (lower prices tend to be more popular)
  if (projectData.price === 0) popularity += 10;
  else if (projectData.price < 25) popularity += 5;

  return Math.min(popularity, 100);
};

export default {
  analyzeProject
};
