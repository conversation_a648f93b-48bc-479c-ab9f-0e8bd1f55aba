import Project from '../models/Project.js';
import { asyncHandler } from '../middleware/errorHandler.js';
import { analyzeProject } from '../services/aiAnalysis.js';
import { analyzeRepository } from '../services/githubService.js';
import { isValidGitHubUrl } from '../utils/helpers.js';

// @desc    Analyze project with AI
// @route   POST /api/ai/analyze/:projectId
// @access  Private (Owner only)
export const analyzeProjectWithAI = asyncHandler(async (req, res) => {
  const { projectId } = req.params;

  const project = await Project.findById(projectId);

  if (!project) {
    return res.status(404).json({
      success: false,
      error: 'Project not found'
    });
  }

  // Check ownership
  if (project.uploadedBy.toString() !== req.user._id.toString()) {
    return res.status(403).json({
      success: false,
      error: 'Access denied. You can only analyze your own projects.'
    });
  }

  try {
    let githubAnalysis = null;

    // Analyze GitHub repository if URL is provided
    if (project.files.githubUrl && isValidGitHubUrl(project.files.githubUrl)) {
      try {
        githubAnalysis = await analyzeRepository(project.files.githubUrl);
      } catch (githubError) {
        console.error('GitHub analysis failed:', githubError);
        // Continue without GitHub analysis
      }
    }

    // Run AI analysis
    const aiAnalysis = await analyzeProject(project, githubAnalysis);

    // Update project with AI analysis
    project.aiAnalysis = aiAnalysis;
    await project.save();

    res.status(200).json({
      success: true,
      message: 'Project analyzed successfully',
      data: {
        analysis: aiAnalysis,
        githubAnalysis: githubAnalysis ? {
          technologies: githubAnalysis.technologies,
          complexity: githubAnalysis.complexity,
          quality: githubAnalysis.quality
        } : null
      }
    });
  } catch (error) {
    console.error('AI analysis error:', error);
    res.status(500).json({
      success: false,
      error: 'Analysis failed. Please try again later.'
    });
  }
});

// @desc    Get AI suggestions for project improvement
// @route   GET /api/ai/suggestions/:projectId
// @access  Private (Owner only)
export const getProjectSuggestions = asyncHandler(async (req, res) => {
  const { projectId } = req.params;

  const project = await Project.findById(projectId);

  if (!project) {
    return res.status(404).json({
      success: false,
      error: 'Project not found'
    });
  }

  // Check ownership
  if (project.uploadedBy.toString() !== req.user._id.toString()) {
    return res.status(403).json({
      success: false,
      error: 'Access denied. You can only get suggestions for your own projects.'
    });
  }

  // Return existing AI analysis if available
  if (project.aiAnalysis && project.aiAnalysis.lastAnalyzed) {
    const daysSinceAnalysis = Math.floor(
      (Date.now() - project.aiAnalysis.lastAnalyzed) / (1000 * 60 * 60 * 24)
    );

    // Return cached analysis if less than 7 days old
    if (daysSinceAnalysis < 7) {
      return res.status(200).json({
        success: true,
        data: {
          suggestions: project.aiAnalysis.suggestedImprovements,
          qualityScore: project.aiAnalysis.qualityScore,
          complexityLevel: project.aiAnalysis.complexityLevel,
          lastAnalyzed: project.aiAnalysis.lastAnalyzed,
          cached: true
        }
      });
    }
  }

  // Run fresh analysis
  try {
    let githubAnalysis = null;

    if (project.files.githubUrl && isValidGitHubUrl(project.files.githubUrl)) {
      try {
        githubAnalysis = await analyzeRepository(project.files.githubUrl);
      } catch (githubError) {
        console.error('GitHub analysis failed:', githubError);
      }
    }

    const aiAnalysis = await analyzeProject(project, githubAnalysis);

    // Update project with new analysis
    project.aiAnalysis = aiAnalysis;
    await project.save();

    res.status(200).json({
      success: true,
      data: {
        suggestions: aiAnalysis.suggestedImprovements,
        qualityScore: aiAnalysis.qualityScore,
        complexityLevel: aiAnalysis.complexityLevel,
        lastAnalyzed: aiAnalysis.lastAnalyzed,
        cached: false
      }
    });
  } catch (error) {
    console.error('AI suggestions error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate suggestions. Please try again later.'
    });
  }
});

// @desc    Get AI-powered project recommendations for buyers
// @route   GET /api/ai/recommendations
// @access  Private
export const getProjectRecommendations = asyncHandler(async (req, res) => {
  const { limit = 10, category, techStack } = req.query;
  const userId = req.user._id;

  try {
    // Get user's purchase history to understand preferences
    const User = (await import('../models/User.js')).default;
    const user = await User.findById(userId).populate('purchaseHistory.project');

    // Build recommendation query based on user preferences
    let recommendationQuery = {
      status: 'approved',
      isActive: true
    };

    // Filter by category if specified
    if (category) {
      recommendationQuery.category = category;
    }

    // Filter by tech stack if specified
    if (techStack) {
      const techArray = Array.isArray(techStack) ? techStack : [techStack];
      recommendationQuery.techStack = { $in: techArray };
    }

    // Get user's preferred categories and technologies from purchase history
    const userPreferences = analyzeUserPreferences(user.purchaseHistory);

    // If no specific filters, use user preferences
    if (!category && !techStack && userPreferences.categories.length > 0) {
      recommendationQuery.$or = [
        { category: { $in: userPreferences.categories } },
        { techStack: { $in: userPreferences.technologies } }
      ];
    }

    // Get projects with AI analysis for better recommendations
    const projects = await Project.find(recommendationQuery)
      .populate('uploadedBy', 'name avatar sellerRating')
      .sort({ 
        'aiAnalysis.estimatedPopularity': -1, 
        averageRating: -1, 
        sales: -1 
      })
      .limit(parseInt(limit) * 2) // Get more to filter
      .select('-files.zipFile');

    // Score and rank projects based on user preferences and AI analysis
    const scoredProjects = projects.map(project => {
      let score = 0;

      // Base score from AI analysis
      if (project.aiAnalysis) {
        score += project.aiAnalysis.estimatedPopularity * 0.3;
        score += project.aiAnalysis.qualityScore * 0.2;
      }

      // Rating and sales score
      score += project.averageRating * 10;
      score += Math.min(project.sales * 2, 20);

      // User preference matching
      if (userPreferences.categories.includes(project.category)) {
        score += 15;
      }

      const techMatches = project.techStack.filter(tech => 
        userPreferences.technologies.includes(tech)
      ).length;
      score += techMatches * 5;

      // Price preference (based on user's purchase history)
      const avgUserSpend = userPreferences.averageSpend;
      if (avgUserSpend > 0) {
        const priceDiff = Math.abs(project.price - avgUserSpend);
        const priceScore = Math.max(0, 20 - (priceDiff / avgUserSpend) * 20);
        score += priceScore;
      }

      return {
        ...project.toObject(),
        recommendationScore: score
      };
    });

    // Sort by recommendation score and limit results
    const recommendations = scoredProjects
      .sort((a, b) => b.recommendationScore - a.recommendationScore)
      .slice(0, parseInt(limit))
      .map(project => {
        // Remove the recommendation score from the response
        const { recommendationScore, ...projectData } = project;
        return projectData;
      });

    res.status(200).json({
      success: true,
      data: {
        recommendations,
        userPreferences: {
          categories: userPreferences.categories,
          technologies: userPreferences.technologies.slice(0, 5),
          averageSpend: userPreferences.averageSpend
        }
      }
    });
  } catch (error) {
    console.error('Recommendations error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate recommendations. Please try again later.'
    });
  }
});

// @desc    Analyze GitHub repository (standalone)
// @route   POST /api/ai/analyze-github
// @access  Private
export const analyzeGitHubRepository = asyncHandler(async (req, res) => {
  const { githubUrl } = req.body;

  if (!githubUrl || !isValidGitHubUrl(githubUrl)) {
    return res.status(400).json({
      success: false,
      error: 'Please provide a valid GitHub repository URL'
    });
  }

  try {
    const analysis = await analyzeRepository(githubUrl);

    res.status(200).json({
      success: true,
      data: {
        analysis
      }
    });
  } catch (error) {
    console.error('GitHub analysis error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to analyze GitHub repository'
    });
  }
});

// Helper function to analyze user preferences
const analyzeUserPreferences = (purchaseHistory) => {
  const preferences = {
    categories: [],
    technologies: [],
    averageSpend: 0
  };

  if (!purchaseHistory || purchaseHistory.length === 0) {
    return preferences;
  }

  const categoryCount = {};
  const techCount = {};
  let totalSpend = 0;

  purchaseHistory.forEach(purchase => {
    if (purchase.project) {
      // Count categories
      const category = purchase.project.category;
      categoryCount[category] = (categoryCount[category] || 0) + 1;

      // Count technologies
      if (purchase.project.techStack) {
        purchase.project.techStack.forEach(tech => {
          techCount[tech] = (techCount[tech] || 0) + 1;
        });
      }

      // Sum spending
      totalSpend += purchase.amount || 0;
    }
  });

  // Get top categories
  preferences.categories = Object.entries(categoryCount)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 3)
    .map(([category]) => category);

  // Get top technologies
  preferences.technologies = Object.entries(techCount)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10)
    .map(([tech]) => tech);

  // Calculate average spend
  preferences.averageSpend = purchaseHistory.length > 0 ? 
    totalSpend / purchaseHistory.length : 0;

  return preferences;
};
