{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.1", "@supabase/supabase-js": "^2.49.8", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "axios": "^1.9.0", "lucide-react": "^0.511.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.4", "react-hot-toast": "^2.5.2", "react-router-dom": "^7.6.1", "yup": "^1.6.1", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.4", "tailwindcss": "^4.1.8", "vite": "^6.3.5"}}