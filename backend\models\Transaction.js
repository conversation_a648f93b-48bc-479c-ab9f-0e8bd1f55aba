import mongoose from 'mongoose';
import { TRANSACTION_STATUS, PAYMENT_METHODS } from '../utils/constants.js';

const transactionSchema = new mongoose.Schema({
  // Transaction identifiers
  transactionId: {
    type: String,
    unique: true,
    required: true
  },
  stripePaymentIntentId: {
    type: String,
    required: true
  },
  stripeChargeId: String,
  
  // Parties involved
  buyer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Buyer reference is required']
  },
  seller: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Seller reference is required']
  },
  project: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Project',
    required: [true, 'Project reference is required']
  },
  
  // Financial details
  amount: {
    type: Number,
    required: [true, 'Transaction amount is required'],
    min: [0, 'Amount cannot be negative']
  },
  currency: {
    type: String,
    default: 'usd',
    uppercase: true
  },
  
  // Platform fees
  platformFee: {
    type: Number,
    required: true,
    min: 0
  },
  platformFeePercentage: {
    type: Number,
    required: true,
    min: 0,
    max: 100
  },
  sellerEarnings: {
    type: Number,
    required: true,
    min: 0
  },
  
  // Payment details
  paymentMethod: {
    type: String,
    enum: Object.values(PAYMENT_METHODS),
    default: PAYMENT_METHODS.STRIPE
  },
  paymentMethodDetails: {
    brand: String, // visa, mastercard, etc.
    last4: String,
    country: String
  },
  
  // Transaction status
  status: {
    type: String,
    enum: Object.values(TRANSACTION_STATUS),
    default: TRANSACTION_STATUS.PENDING
  },
  
  // Timestamps for different stages
  initiatedAt: {
    type: Date,
    default: Date.now
  },
  completedAt: Date,
  failedAt: Date,
  refundedAt: Date,
  cancelledAt: Date,
  
  // Failure details
  failureReason: String,
  failureCode: String,
  
  // Refund details
  refundAmount: {
    type: Number,
    min: 0
  },
  refundReason: String,
  refundRequestedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  refundProcessedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  // Download tracking
  downloadCount: {
    type: Number,
    default: 0
  },
  downloadLimit: {
    type: Number,
    default: 10 // Allow 10 downloads per purchase
  },
  firstDownloadAt: Date,
  lastDownloadAt: Date,
  
  // Additional metadata
  metadata: {
    userAgent: String,
    ipAddress: String,
    country: String,
    couponCode: String,
    discountAmount: Number
  },
  
  // Review tracking
  reviewSubmitted: {
    type: Boolean,
    default: false
  },
  reviewId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Review'
  },
  
  // Dispute handling
  isDisputed: {
    type: Boolean,
    default: false
  },
  disputeReason: String,
  disputeStatus: {
    type: String,
    enum: ['open', 'under_review', 'resolved', 'closed']
  },
  disputeResolvedAt: Date,
  
  // Seller payout tracking
  payoutStatus: {
    type: String,
    enum: ['pending', 'processing', 'paid', 'failed'],
    default: 'pending'
  },
  payoutId: String, // Stripe transfer ID
  payoutDate: Date,
  payoutAmount: Number
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
transactionSchema.index({ transactionId: 1 }, { unique: true });
transactionSchema.index({ stripePaymentIntentId: 1 });
transactionSchema.index({ buyer: 1 });
transactionSchema.index({ seller: 1 });
transactionSchema.index({ project: 1 });
transactionSchema.index({ status: 1 });
transactionSchema.index({ createdAt: -1 });
transactionSchema.index({ completedAt: -1 });

// Virtual for download availability
transactionSchema.virtual('canDownload').get(function() {
  return this.status === TRANSACTION_STATUS.COMPLETED && 
         this.downloadCount < this.downloadLimit;
});

// Virtual for days since purchase
transactionSchema.virtual('daysSincePurchase').get(function() {
  if (!this.completedAt) return null;
  const diffTime = Math.abs(new Date() - this.completedAt);
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
});

// Pre-save middleware to generate transaction ID
transactionSchema.pre('save', function(next) {
  if (!this.transactionId) {
    this.transactionId = `TXN_${Date.now()}_${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
  }
  
  // Calculate platform fee and seller earnings
  if (this.isModified('amount') || this.isModified('platformFeePercentage')) {
    this.platformFee = Math.round(this.amount * (this.platformFeePercentage / 100) * 100) / 100;
    this.sellerEarnings = Math.round((this.amount - this.platformFee) * 100) / 100;
  }
  
  next();
});

// Post-save middleware to update project sales
transactionSchema.post('save', async function() {
  if (this.status === TRANSACTION_STATUS.COMPLETED && this.isModified('status')) {
    try {
      const Project = mongoose.model('Project');
      await Project.findByIdAndUpdate(this.project, {
        $inc: { 
          sales: 1,
          revenue: this.sellerEarnings
        }
      });
      
      // Update seller stats
      const User = mongoose.model('User');
      await User.findByIdAndUpdate(this.seller, {
        $inc: {
          totalSales: 1,
          totalEarnings: this.sellerEarnings
        },
        $push: {
          'purchaseHistory': {
            project: this.project,
            purchasedAt: this.completedAt || new Date(),
            amount: this.amount
          }
        }
      });
      
      // Update buyer purchase history
      await User.findByIdAndUpdate(this.buyer, {
        $push: {
          'purchaseHistory': {
            project: this.project,
            purchasedAt: this.completedAt || new Date(),
            amount: this.amount
          }
        }
      });
    } catch (error) {
      console.error('Error updating project/user stats:', error);
    }
  }
});

// Static method to get transaction statistics
transactionSchema.statics.getStats = async function(filters = {}) {
  const matchStage = { status: TRANSACTION_STATUS.COMPLETED };
  
  if (filters.sellerId) {
    matchStage.seller = mongoose.Types.ObjectId(filters.sellerId);
  }
  
  if (filters.startDate && filters.endDate) {
    matchStage.completedAt = {
      $gte: new Date(filters.startDate),
      $lte: new Date(filters.endDate)
    };
  }
  
  const stats = await this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalTransactions: { $sum: 1 },
        totalRevenue: { $sum: '$amount' },
        totalSellerEarnings: { $sum: '$sellerEarnings' },
        totalPlatformFees: { $sum: '$platformFee' },
        averageTransactionValue: { $avg: '$amount' }
      }
    }
  ]);
  
  return stats[0] || {
    totalTransactions: 0,
    totalRevenue: 0,
    totalSellerEarnings: 0,
    totalPlatformFees: 0,
    averageTransactionValue: 0
  };
};

// Instance method to mark as completed
transactionSchema.methods.markAsCompleted = function() {
  this.status = TRANSACTION_STATUS.COMPLETED;
  this.completedAt = new Date();
  return this.save();
};

// Instance method to mark as failed
transactionSchema.methods.markAsFailed = function(reason, code) {
  this.status = TRANSACTION_STATUS.FAILED;
  this.failedAt = new Date();
  this.failureReason = reason;
  this.failureCode = code;
  return this.save();
};

// Instance method to process refund
transactionSchema.methods.processRefund = function(amount, reason, processedBy) {
  this.status = TRANSACTION_STATUS.REFUNDED;
  this.refundedAt = new Date();
  this.refundAmount = amount || this.amount;
  this.refundReason = reason;
  this.refundProcessedBy = processedBy;
  return this.save();
};

// Instance method to track download
transactionSchema.methods.trackDownload = function() {
  this.downloadCount += 1;
  this.lastDownloadAt = new Date();
  
  if (!this.firstDownloadAt) {
    this.firstDownloadAt = new Date();
  }
  
  return this.save();
};

export default mongoose.model('Transaction', transactionSchema);
