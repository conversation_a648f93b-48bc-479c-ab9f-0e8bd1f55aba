import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

// Check if Supabase is properly configured
const isSupabaseConfigured = supabaseUrl &&
  supabaseAnonKey &&
  supabaseServiceKey &&
  !supabaseUrl.includes('your-project') &&
  !supabaseUrl.includes('placeholder') &&
  !supabaseAnonKey.includes('placeholder') &&
  supabaseUrl.includes('supabase.co');

if (!isSupabaseConfigured) {
  console.warn('⚠️ Supabase not configured. Please update your environment variables.');
  console.log('Debug - Environment variables:');
  console.log('SUPABASE_URL:', supabaseUrl ? 'Set' : 'Not set');
  console.log('SUPABASE_ANON_KEY:', supabaseAnonKey ? 'Set' : 'Not set');
  console.log('SUPABASE_SERVICE_ROLE_KEY:', supabaseServiceKey ? 'Set' : 'Not set');
  if (supabaseUrl) {
    console.log('URL contains placeholder:', supabaseUrl.includes('your-project') || supabaseUrl.includes('placeholder'));
    console.log('URL contains supabase.co:', supabaseUrl.includes('supabase.co'));
  }
}

// Client for general operations (with RLS)
export const supabase = isSupabaseConfigured ?
  createClient(supabaseUrl, supabaseAnonKey) :
  {
    auth: {
      getUser: () => Promise.reject(new Error('Supabase not configured')),
      signInWithPassword: () => Promise.reject(new Error('Supabase not configured'))
    },
    from: () => ({
      select: () => Promise.reject(new Error('Supabase not configured'))
    })
  };

// Admin client for server-side operations (bypasses RLS)
export const supabaseAdmin = isSupabaseConfigured ?
  createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }) : supabase;

// Verify Supabase connection
export const verifySupabaseConnection = async () => {
  if (!isSupabaseConfigured) {
    console.log('⚠️ Supabase not configured - skipping connection test');
    return false;
  }

  try {
    const { data, error } = await supabase.from('users').select('count').limit(1);
    if (error && error.code !== 'PGRST116') { // PGRST116 is "table not found" which is expected initially
      console.error('❌ Supabase connection failed:', error);
      return false;
    }
    console.log('✅ Supabase connected successfully');
    return true;
  } catch (error) {
    console.error('❌ Supabase connection error:', error);
    return false;
  }
};

// Helper function to get user from Supabase token
export const getUserFromToken = async (token) => {
  if (!isSupabaseConfigured) {
    throw new Error('Supabase not configured');
  }

  try {
    const { data: { user }, error } = await supabase.auth.getUser(token);
    if (error) {
      throw error;
    }
    return user;
  } catch (error) {
    console.error('Error getting user from token:', error);
    throw new Error('Invalid token');
  }
};

// Helper function to verify user session
export const verifyUserSession = async (accessToken) => {
  if (!isSupabaseConfigured) {
    throw new Error('Supabase not configured');
  }

  try {
    const { data: { user }, error } = await supabase.auth.getUser(accessToken);
    if (error || !user) {
      throw new Error('Invalid session');
    }
    return user;
  } catch (error) {
    console.error('Session verification failed:', error);
    throw new Error('Session verification failed');
  }
};

// Helper function to create user profile in our MongoDB
export const syncUserWithMongoDB = async (supabaseUser) => {
  try {
    const User = (await import('../models/User.js')).default;
    
    // Check if user already exists in MongoDB
    let user = await User.findOne({ email: supabaseUser.email });
    
    if (!user) {
      // Create new user in MongoDB
      user = await User.create({
        name: supabaseUser.user_metadata?.full_name || supabaseUser.email.split('@')[0],
        email: supabaseUser.email,
        supabaseId: supabaseUser.id,
        avatar: {
          url: supabaseUser.user_metadata?.avatar_url || null
        },
        isVerified: supabaseUser.email_confirmed_at ? true : false,
        role: 'buyer' // Default role
      });
    } else {
      // Update existing user with Supabase ID if not set
      if (!user.supabaseId) {
        user.supabaseId = supabaseUser.id;
        await user.save();
      }
    }
    
    return user;
  } catch (error) {
    console.error('Error syncing user with MongoDB:', error);
    throw error;
  }
};

// Helper function to handle user sign up
export const handleUserSignUp = async (email, password, userData = {}) => {
  try {
    const { data, error } = await supabaseAdmin.auth.admin.createUser({
      email,
      password,
      user_metadata: {
        full_name: userData.name || email.split('@')[0],
        role: userData.role || 'buyer'
      },
      email_confirm: true // Auto-confirm for development
    });
    
    if (error) {
      throw error;
    }
    
    // Sync with MongoDB
    const mongoUser = await syncUserWithMongoDB(data.user);
    
    return {
      supabaseUser: data.user,
      mongoUser
    };
  } catch (error) {
    console.error('Error in user sign up:', error);
    throw error;
  }
};

// Helper function to handle user sign in
export const handleUserSignIn = async (email, password) => {
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });
    
    if (error) {
      throw error;
    }
    
    // Sync with MongoDB
    const mongoUser = await syncUserWithMongoDB(data.user);
    
    return {
      supabaseUser: data.user,
      mongoUser,
      session: data.session
    };
  } catch (error) {
    console.error('Error in user sign in:', error);
    throw error;
  }
};

// Helper function to handle password reset
export const handlePasswordReset = async (email) => {
  try {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${process.env.FRONTEND_URL}/reset-password`
    });
    
    if (error) {
      throw error;
    }
    
    return { success: true };
  } catch (error) {
    console.error('Error in password reset:', error);
    throw error;
  }
};

// Helper function to update user password
export const updateUserPassword = async (accessToken, newPassword) => {
  try {
    const { error } = await supabaseAdmin.auth.admin.updateUserById(
      accessToken,
      { password: newPassword }
    );
    
    if (error) {
      throw error;
    }
    
    return { success: true };
  } catch (error) {
    console.error('Error updating password:', error);
    throw error;
  }
};

// Helper function to delete user
export const deleteSupabaseUser = async (userId) => {
  try {
    const { error } = await supabaseAdmin.auth.admin.deleteUser(userId);
    
    if (error) {
      throw error;
    }
    
    return { success: true };
  } catch (error) {
    console.error('Error deleting user:', error);
    throw error;
  }
};

export default supabase;
