import multer from 'multer';
import path from 'path';
import { fileURLToPath } from 'url';
import { ALLOWED_FILE_TYPES, FILE_SIZE_LIMITS } from '../utils/constants.js';
import { sanitizeFilename } from '../utils/helpers.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    // Create uploads directory structure
    let uploadPath = path.join(__dirname, '../uploads');
    
    if (file.fieldname === 'avatar') {
      uploadPath = path.join(uploadPath, 'avatars');
    } else if (file.fieldname === 'projectZip') {
      uploadPath = path.join(uploadPath, 'projects');
    } else if (file.fieldname === 'previewImages') {
      uploadPath = path.join(uploadPath, 'previews');
    }
    
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    // Generate unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const sanitizedName = sanitizeFilename(file.originalname);
    const extension = path.extname(sanitizedName);
    const baseName = path.basename(sanitizedName, extension);
    
    cb(null, `${baseName}-${uniqueSuffix}${extension}`);
  }
});

// File filter function
const fileFilter = (req, file, cb) => {
  let allowedTypes = [];
  
  if (file.fieldname === 'avatar') {
    allowedTypes = ALLOWED_FILE_TYPES.IMAGES;
  } else if (file.fieldname === 'projectZip') {
    allowedTypes = ALLOWED_FILE_TYPES.ARCHIVES;
  } else if (file.fieldname === 'previewImages') {
    allowedTypes = ALLOWED_FILE_TYPES.IMAGES;
  } else if (file.fieldname === 'documents') {
    allowedTypes = ALLOWED_FILE_TYPES.DOCUMENTS;
  }
  
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error(`Invalid file type. Allowed types: ${allowedTypes.join(', ')}`), false);
  }
};

// Create multer instance
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: FILE_SIZE_LIMITS.ZIP, // Maximum file size
    files: 10 // Maximum number of files
  }
});

// Specific upload configurations
export const uploadAvatar = upload.single('avatar');

export const uploadProjectFiles = upload.fields([
  { name: 'projectZip', maxCount: 1 },
  { name: 'previewImages', maxCount: 5 }
]);

export const uploadPreviewImages = upload.array('previewImages', 5);

export const uploadSingleImage = upload.single('image');

// Error handling middleware for multer
export const handleUploadError = (error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        error: 'File too large. Maximum size allowed is 100MB.'
      });
    }
    
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        success: false,
        error: 'Too many files. Maximum 10 files allowed.'
      });
    }
    
    if (error.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({
        success: false,
        error: 'Unexpected file field.'
      });
    }
  }
  
  if (error.message.includes('Invalid file type')) {
    return res.status(400).json({
      success: false,
      error: error.message
    });
  }
  
  next(error);
};

// Middleware to validate uploaded files
export const validateUploadedFiles = (req, res, next) => {
  // Check if files were uploaded when required
  if (req.route.path.includes('/upload') && !req.files && !req.file) {
    return res.status(400).json({
      success: false,
      error: 'No files uploaded'
    });
  }
  
  // Validate file sizes based on type
  if (req.files) {
    for (const fieldname in req.files) {
      const files = req.files[fieldname];
      
      for (const file of files) {
        let maxSize;
        
        if (fieldname === 'avatar' || fieldname === 'previewImages') {
          maxSize = FILE_SIZE_LIMITS.IMAGE;
        } else if (fieldname === 'projectZip') {
          maxSize = FILE_SIZE_LIMITS.ZIP;
        } else if (fieldname === 'documents') {
          maxSize = FILE_SIZE_LIMITS.DOCUMENT;
        }
        
        if (file.size > maxSize) {
          return res.status(400).json({
            success: false,
            error: `File ${file.originalname} exceeds maximum size limit`
          });
        }
      }
    }
  }
  
  if (req.file) {
    let maxSize;
    
    if (req.file.fieldname === 'avatar') {
      maxSize = FILE_SIZE_LIMITS.IMAGE;
    } else if (req.file.fieldname === 'projectZip') {
      maxSize = FILE_SIZE_LIMITS.ZIP;
    }
    
    if (req.file.size > maxSize) {
      return res.status(400).json({
        success: false,
        error: `File ${req.file.originalname} exceeds maximum size limit`
      });
    }
  }
  
  next();
};

export default upload;
