import express from 'express';
import {
  register,
  login,
  getMe,
  updateProfile,
  changePassword,
  logout,
  deleteAccount,
  getUserStats
} from '../controllers/authController.js';
import {
  validateUserRegistration,
  validateUserLogin,
  validateUserUpdate,
  validatePasswordChange
} from '../middleware/validation.js';
import { protect, sensitiveOperationLimit } from '../middleware/auth.js';

const router = express.Router();

// Public routes
router.post('/register', validateUserRegistration, register);
router.post('/login', validateUserLogin, login);

// Protected routes
router.use(protect); // All routes below require authentication

router.get('/me', getMe);
router.get('/stats', getUserStats);
router.put('/profile', validateUserUpdate, updateProfile);
router.put('/password', sensitiveOperationLimit, validatePasswordChange, changePassword);
router.post('/logout', logout);
router.delete('/account', sensitiveOperationLimit, deleteAccount);

export default router;
