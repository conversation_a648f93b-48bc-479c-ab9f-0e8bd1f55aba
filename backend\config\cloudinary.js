import { v2 as cloudinary } from 'cloudinary';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

export const cloudinaryConfig = () => {
  cloudinary.config({
    cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
    api_key: process.env.CLOUDINARY_API_KEY,
    api_secret: process.env.CLOUDINARY_API_SECRET,
    secure: true
  });

  console.log('✅ Cloudinary configured successfully');
};

// Upload file to Cloudinary
export const uploadToCloudinary = async (filePath, folder = 'marketplace') => {
  try {
    const result = await cloudinary.uploader.upload(filePath, {
      folder: folder,
      resource_type: 'auto', // Automatically detect file type
      use_filename: true,
      unique_filename: true,
    });

    return {
      public_id: result.public_id,
      url: result.secure_url,
      format: result.format,
      resource_type: result.resource_type,
      bytes: result.bytes
    };
  } catch (error) {
    console.error('❌ Cloudinary upload error:', error);
    throw new Error('File upload failed');
  }
};

// Upload ZIP file specifically
export const uploadZipToCloudinary = async (filePath, projectId) => {
  try {
    const result = await cloudinary.uploader.upload(filePath, {
      folder: `marketplace/projects/${projectId}`,
      resource_type: 'raw', // For ZIP files
      use_filename: true,
      unique_filename: true,
    });

    return {
      public_id: result.public_id,
      url: result.secure_url,
      bytes: result.bytes
    };
  } catch (error) {
    console.error('❌ Cloudinary ZIP upload error:', error);
    throw new Error('ZIP file upload failed');
  }
};

// Upload image to Cloudinary
export const uploadImageToCloudinary = async (filePath, projectId) => {
  try {
    const result = await cloudinary.uploader.upload(filePath, {
      folder: `marketplace/previews/${projectId}`,
      resource_type: 'image',
      transformation: [
        { width: 1200, height: 800, crop: 'limit' },
        { quality: 'auto' },
        { format: 'auto' }
      ],
      use_filename: true,
      unique_filename: true,
    });

    return {
      public_id: result.public_id,
      url: result.secure_url,
      width: result.width,
      height: result.height,
      bytes: result.bytes
    };
  } catch (error) {
    console.error('❌ Cloudinary image upload error:', error);
    throw new Error('Image upload failed');
  }
};

// Delete file from Cloudinary
export const deleteFromCloudinary = async (publicId, resourceType = 'image') => {
  try {
    const result = await cloudinary.uploader.destroy(publicId, {
      resource_type: resourceType
    });
    return result;
  } catch (error) {
    console.error('❌ Cloudinary delete error:', error);
    throw new Error('File deletion failed');
  }
};

export default cloudinary;
