import Review from '../models/Review.js';
import Project from '../models/Project.js';
import Transaction from '../models/Transaction.js';
import User from '../models/User.js';
import { asyncHandler } from '../middleware/errorHandler.js';
import { TRANSACTION_STATUS, SUCCESS_MESSAGES } from '../utils/constants.js';
import { formatPaginationResponse } from '../utils/helpers.js';

// @desc    Create a review for a purchased project
// @route   POST /api/reviews
// @access  Private
export const createReview = asyncHandler(async (req, res) => {
  const {
    projectId,
    rating,
    comment,
    detailedRatings
  } = req.body;
  const buyerId = req.user._id;

  // Check if project exists
  const project = await Project.findById(projectId);
  if (!project) {
    return res.status(404).json({
      success: false,
      error: 'Project not found'
    });
  }

  // Check if user purchased this project
  const transaction = await Transaction.findOne({
    project: projectId,
    buyer: buyerId,
    status: TRANSACTION_STATUS.COMPLETED
  });

  if (!transaction) {
    return res.status(403).json({
      success: false,
      error: 'You can only review projects you have purchased'
    });
  }

  // Check if user already reviewed this project
  const existingReview = await Review.findOne({
    project: projectId,
    buyer: buyerId
  });

  if (existingReview) {
    return res.status(400).json({
      success: false,
      error: 'You have already reviewed this project'
    });
  }

  try {
    // Create review
    const review = await Review.create({
      project: projectId,
      buyer: buyerId,
      seller: project.uploadedBy,
      transaction: transaction._id,
      rating,
      comment,
      detailedRatings,
      isVerified: true // Verified because they purchased it
    });

    // Mark transaction as reviewed
    transaction.reviewSubmitted = true;
    transaction.reviewId = review._id;
    await transaction.save();

    // Populate review data for response
    await review.populate('buyer', 'name avatar');

    res.status(201).json({
      success: true,
      message: SUCCESS_MESSAGES.REVIEW_CREATED,
      data: {
        review
      }
    });
  } catch (error) {
    console.error('Review creation error:', error);
    res.status(400).json({
      success: false,
      error: error.message || 'Failed to create review'
    });
  }
});

// @desc    Get reviews for a project
// @route   GET /api/reviews/project/:projectId
// @access  Public
export const getProjectReviews = asyncHandler(async (req, res) => {
  const { projectId } = req.params;
  const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;

  // Check if project exists
  const project = await Project.findById(projectId);
  if (!project) {
    return res.status(404).json({
      success: false,
      error: 'Project not found'
    });
  }

  const reviews = await Review.getProjectReviews(projectId, {
    page: parseInt(page),
    limit: parseInt(limit),
    sortBy,
    sortOrder
  });

  const totalReviews = await Review.countDocuments({
    project: projectId,
    isModerated: false
  });

  // Get review statistics
  const reviewStats = await Review.getReviewStats(projectId);

  const response = formatPaginationResponse(
    reviews,
    totalReviews,
    parseInt(page),
    parseInt(limit)
  );

  res.status(200).json({
    success: true,
    data: {
      ...response,
      stats: reviewStats
    }
  });
});

// @desc    Get reviews for a seller
// @route   GET /api/reviews/seller/:sellerId
// @access  Public
export const getSellerReviews = asyncHandler(async (req, res) => {
  const { sellerId } = req.params;
  const { page = 1, limit = 10 } = req.query;

  // Check if seller exists
  const seller = await User.findById(sellerId);
  if (!seller) {
    return res.status(404).json({
      success: false,
      error: 'Seller not found'
    });
  }

  const reviews = await Review.getSellerReviews(sellerId, {
    page: parseInt(page),
    limit: parseInt(limit)
  });

  const totalReviews = await Review.countDocuments({
    seller: sellerId,
    isModerated: false
  });

  const response = formatPaginationResponse(
    reviews,
    totalReviews,
    parseInt(page),
    parseInt(limit)
  );

  res.status(200).json({
    success: true,
    data: response
  });
});

// @desc    Update a review
// @route   PUT /api/reviews/:reviewId
// @access  Private (Review owner only)
export const updateReview = asyncHandler(async (req, res) => {
  const { reviewId } = req.params;
  const { rating, comment, detailedRatings } = req.body;
  const buyerId = req.user._id;

  const review = await Review.findById(reviewId);

  if (!review) {
    return res.status(404).json({
      success: false,
      error: 'Review not found'
    });
  }

  // Check ownership
  if (review.buyer.toString() !== buyerId.toString()) {
    return res.status(403).json({
      success: false,
      error: 'Access denied. You can only update your own reviews.'
    });
  }

  // Check if review is too old to edit (30 days limit)
  const daysSinceCreation = Math.floor(
    (Date.now() - review.createdAt) / (1000 * 60 * 60 * 24)
  );

  if (daysSinceCreation > 30) {
    return res.status(400).json({
      success: false,
      error: 'Reviews can only be edited within 30 days of creation'
    });
  }

  try {
    // Update review
    review.rating = rating || review.rating;
    review.comment = comment || review.comment;
    review.detailedRatings = detailedRatings || review.detailedRatings;
    
    await review.save();

    await review.populate('buyer', 'name avatar');

    res.status(200).json({
      success: true,
      message: 'Review updated successfully',
      data: {
        review
      }
    });
  } catch (error) {
    console.error('Review update error:', error);
    res.status(400).json({
      success: false,
      error: error.message || 'Failed to update review'
    });
  }
});

// @desc    Delete a review
// @route   DELETE /api/reviews/:reviewId
// @access  Private (Review owner only)
export const deleteReview = asyncHandler(async (req, res) => {
  const { reviewId } = req.params;
  const buyerId = req.user._id;

  const review = await Review.findById(reviewId);

  if (!review) {
    return res.status(404).json({
      success: false,
      error: 'Review not found'
    });
  }

  // Check ownership
  if (review.buyer.toString() !== buyerId.toString()) {
    return res.status(403).json({
      success: false,
      error: 'Access denied. You can only delete your own reviews.'
    });
  }

  try {
    await Review.findByIdAndDelete(reviewId);

    // Update transaction to mark review as not submitted
    await Transaction.findByIdAndUpdate(review.transaction, {
      reviewSubmitted: false,
      reviewId: null
    });

    res.status(200).json({
      success: true,
      message: 'Review deleted successfully'
    });
  } catch (error) {
    console.error('Review deletion error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete review'
    });
  }
});

// @desc    Add seller response to a review
// @route   POST /api/reviews/:reviewId/response
// @access  Private (Seller only)
export const addSellerResponse = asyncHandler(async (req, res) => {
  const { reviewId } = req.params;
  const { comment } = req.body;
  const sellerId = req.user._id;

  const review = await Review.findById(reviewId);

  if (!review) {
    return res.status(404).json({
      success: false,
      error: 'Review not found'
    });
  }

  // Check if user is the seller of the reviewed project
  if (review.seller.toString() !== sellerId.toString()) {
    return res.status(403).json({
      success: false,
      error: 'Access denied. You can only respond to reviews of your projects.'
    });
  }

  // Check if seller already responded
  if (review.sellerResponse && review.sellerResponse.comment) {
    return res.status(400).json({
      success: false,
      error: 'You have already responded to this review'
    });
  }

  try {
    await review.addSellerResponse(comment, sellerId);

    await review.populate('buyer', 'name avatar');

    res.status(200).json({
      success: true,
      message: 'Response added successfully',
      data: {
        review
      }
    });
  } catch (error) {
    console.error('Seller response error:', error);
    res.status(400).json({
      success: false,
      error: 'Failed to add response'
    });
  }
});

// @desc    Mark review as helpful
// @route   POST /api/reviews/:reviewId/helpful
// @access  Private
export const markReviewAsHelpful = asyncHandler(async (req, res) => {
  const { reviewId } = req.params;

  const review = await Review.findById(reviewId);

  if (!review) {
    return res.status(404).json({
      success: false,
      error: 'Review not found'
    });
  }

  try {
    await review.markAsHelpful();

    res.status(200).json({
      success: true,
      message: 'Review marked as helpful',
      data: {
        helpfulCount: review.isHelpful
      }
    });
  } catch (error) {
    console.error('Mark helpful error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to mark review as helpful'
    });
  }
});

// @desc    Report a review
// @route   POST /api/reviews/:reviewId/report
// @access  Private
export const reportReview = asyncHandler(async (req, res) => {
  const { reviewId } = req.params;
  const { reason } = req.body;

  const review = await Review.findById(reviewId);

  if (!review) {
    return res.status(404).json({
      success: false,
      error: 'Review not found'
    });
  }

  try {
    review.isReported = true;
    review.reportReason = reason;
    await review.save();

    res.status(200).json({
      success: true,
      message: 'Review reported successfully. Our team will review it.'
    });
  } catch (error) {
    console.error('Report review error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to report review'
    });
  }
});

// @desc    Get user's reviews (reviews they wrote)
// @route   GET /api/reviews/my-reviews
// @access  Private
export const getMyReviews = asyncHandler(async (req, res) => {
  const { page = 1, limit = 10 } = req.query;
  const buyerId = req.user._id;

  const skip = (parseInt(page) - 1) * parseInt(limit);

  const reviews = await Review.find({ buyer: buyerId })
    .populate('project', 'title slug category price files.previewImages')
    .populate('seller', 'name avatar')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(parseInt(limit));

  const totalReviews = await Review.countDocuments({ buyer: buyerId });

  const response = formatPaginationResponse(
    reviews,
    totalReviews,
    parseInt(page),
    parseInt(limit)
  );

  res.status(200).json({
    success: true,
    data: response
  });
});
