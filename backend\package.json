{"name": "marketplace-backend", "version": "1.0.0", "description": "AI-augmented marketplace platform backend", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "keywords": ["marketplace", "ai", "web-development", "mern"], "author": "Your Name", "license": "MIT", "dependencies": {"@supabase/supabase-js": "^2.49.8", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "cloudinary": "^1.41.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "express-async-errors": "^3.1.1", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "openai": "^4.20.1", "stripe": "^14.7.0"}, "devDependencies": {"eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}}