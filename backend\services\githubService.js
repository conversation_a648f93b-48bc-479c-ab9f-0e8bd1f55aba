import axios from 'axios';
import { extractGitHubInfo } from '../utils/helpers.js';

// GitHub API configuration
const GITHUB_API_BASE = 'https://api.github.com';
const GITHUB_TOKEN = process.env.GITHUB_TOKEN;

// Create axios instance with GitHub token
const githubApi = axios.create({
  baseURL: GITHUB_API_BASE,
  headers: {
    'Authorization': `token ${GITHUB_TOKEN}`,
    'Accept': 'application/vnd.github.v3+json',
    'User-Agent': 'Marketplace-Platform'
  }
});

// Get repository information
export const getRepositoryInfo = async (githubUrl) => {
  try {
    const repoInfo = extractGitHubInfo(githubUrl);
    if (!repoInfo) {
      throw new Error('Invalid GitHub URL');
    }

    const { owner, repo } = repoInfo;

    // Get basic repository information
    const repoResponse = await githubApi.get(`/repos/${owner}/${repo}`);
    const repoData = repoResponse.data;

    // Get repository contents (root directory)
    const contentsResponse = await githubApi.get(`/repos/${owner}/${repo}/contents`);
    const contents = contentsResponse.data;

    // Get repository languages
    const languagesResponse = await githubApi.get(`/repos/${owner}/${repo}/languages`);
    const languages = languagesResponse.data;

    // Get latest commits (for activity analysis)
    const commitsResponse = await githubApi.get(`/repos/${owner}/${repo}/commits?per_page=10`);
    const commits = commitsResponse.data;

    // Get README content if available
    let readmeContent = null;
    try {
      const readmeResponse = await githubApi.get(`/repos/${owner}/${repo}/readme`);
      readmeContent = Buffer.from(readmeResponse.data.content, 'base64').toString('utf-8');
    } catch (error) {
      console.log('No README found or error fetching README');
    }

    return {
      basic: {
        name: repoData.name,
        fullName: repoData.full_name,
        description: repoData.description,
        stars: repoData.stargazers_count,
        forks: repoData.forks_count,
        watchers: repoData.watchers_count,
        size: repoData.size,
        language: repoData.language,
        createdAt: repoData.created_at,
        updatedAt: repoData.updated_at,
        pushedAt: repoData.pushed_at,
        defaultBranch: repoData.default_branch,
        topics: repoData.topics || [],
        license: repoData.license?.name || null,
        isPrivate: repoData.private,
        hasIssues: repoData.has_issues,
        hasWiki: repoData.has_wiki,
        hasPages: repoData.has_pages
      },
      contents: contents.map(item => ({
        name: item.name,
        type: item.type,
        size: item.size,
        path: item.path
      })),
      languages,
      commits: commits.map(commit => ({
        sha: commit.sha,
        message: commit.commit.message,
        author: commit.commit.author.name,
        date: commit.commit.author.date
      })),
      readme: readmeContent
    };
  } catch (error) {
    console.error('GitHub API error:', error.response?.data || error.message);
    throw new Error(`Failed to fetch repository information: ${error.response?.data?.message || error.message}`);
  }
};

// Analyze repository structure and detect technologies
export const analyzeRepository = async (githubUrl) => {
  try {
    const repoInfo = await getRepositoryInfo(githubUrl);
    
    // Detect technologies based on files and languages
    const detectedTechnologies = detectTechnologies(repoInfo.contents, repoInfo.languages);
    
    // Analyze project structure
    const projectStructure = analyzeProjectStructure(repoInfo.contents);
    
    // Calculate complexity score
    const complexityScore = calculateComplexityScore(repoInfo);
    
    // Generate quality metrics
    const qualityMetrics = generateQualityMetrics(repoInfo);
    
    // Extract features from README
    const extractedFeatures = extractFeaturesFromReadme(repoInfo.readme);
    
    return {
      technologies: detectedTechnologies,
      structure: projectStructure,
      complexity: complexityScore,
      quality: qualityMetrics,
      features: extractedFeatures,
      metadata: {
        stars: repoInfo.basic.stars,
        forks: repoInfo.basic.forks,
        lastUpdate: repoInfo.basic.pushedAt,
        license: repoInfo.basic.license,
        topics: repoInfo.basic.topics
      }
    };
  } catch (error) {
    console.error('Repository analysis error:', error);
    throw error;
  }
};

// Detect technologies based on file extensions and package files
const detectTechnologies = (contents, languages) => {
  const technologies = new Set();
  const packageFiles = new Set();
  
  // Check for specific files that indicate technologies
  contents.forEach(file => {
    const fileName = file.name.toLowerCase();
    
    // Package managers and config files
    if (fileName === 'package.json') {
      technologies.add('Node.js');
      packageFiles.add('package.json');
    }
    if (fileName === 'requirements.txt' || fileName === 'pyproject.toml') {
      technologies.add('Python');
      packageFiles.add(fileName);
    }
    if (fileName === 'composer.json') {
      technologies.add('PHP');
      packageFiles.add('composer.json');
    }
    if (fileName === 'cargo.toml') {
      technologies.add('Rust');
      packageFiles.add('cargo.toml');
    }
    if (fileName === 'go.mod') {
      technologies.add('Go');
      packageFiles.add('go.mod');
    }
    if (fileName === 'pom.xml' || fileName === 'build.gradle') {
      technologies.add('Java');
      packageFiles.add(fileName);
    }
    if (fileName === 'gemfile') {
      technologies.add('Ruby');
      packageFiles.add('gemfile');
    }
    
    // Framework-specific files
    if (fileName === 'next.config.js') technologies.add('Next.js');
    if (fileName === 'nuxt.config.js') technologies.add('Nuxt.js');
    if (fileName === 'angular.json') technologies.add('Angular');
    if (fileName === 'vue.config.js') technologies.add('Vue.js');
    if (fileName === 'svelte.config.js') technologies.add('Svelte');
    if (fileName === 'gatsby-config.js') technologies.add('Gatsby');
    if (fileName === 'vite.config.js') technologies.add('Vite');
    if (fileName === 'webpack.config.js') technologies.add('Webpack');
    if (fileName === 'tailwind.config.js') technologies.add('Tailwind CSS');
    if (fileName === 'dockerfile') technologies.add('Docker');
    if (fileName === 'docker-compose.yml') technologies.add('Docker Compose');
    
    // Database files
    if (fileName.includes('mongodb') || fileName.includes('mongo')) technologies.add('MongoDB');
    if (fileName.includes('mysql')) technologies.add('MySQL');
    if (fileName.includes('postgres')) technologies.add('PostgreSQL');
    if (fileName.includes('redis')) technologies.add('Redis');
  });
  
  // Add languages from GitHub API
  Object.keys(languages).forEach(lang => {
    technologies.add(lang);
  });
  
  return {
    detected: Array.from(technologies),
    packageFiles: Array.from(packageFiles),
    primaryLanguage: Object.keys(languages)[0] || null,
    languageDistribution: languages
  };
};

// Analyze project structure
const analyzeProjectStructure = (contents) => {
  const structure = {
    hasTests: false,
    hasDocumentation: false,
    hasConfigFiles: false,
    hasSourceCode: false,
    directories: [],
    files: []
  };
  
  contents.forEach(item => {
    if (item.type === 'dir') {
      structure.directories.push(item.name);
      
      // Check for common directory patterns
      const dirName = item.name.toLowerCase();
      if (['test', 'tests', '__tests__', 'spec'].includes(dirName)) {
        structure.hasTests = true;
      }
      if (['docs', 'documentation', 'doc'].includes(dirName)) {
        structure.hasDocumentation = true;
      }
      if (['src', 'source', 'lib', 'app'].includes(dirName)) {
        structure.hasSourceCode = true;
      }
    } else {
      structure.files.push(item.name);
      
      // Check for config files
      const fileName = item.name.toLowerCase();
      if (fileName.includes('config') || fileName.includes('.env') || 
          fileName.endsWith('.json') || fileName.endsWith('.yml') || 
          fileName.endsWith('.yaml')) {
        structure.hasConfigFiles = true;
      }
    }
  });
  
  return structure;
};

// Calculate complexity score based on various factors
const calculateComplexityScore = (repoInfo) => {
  let score = 0;
  
  // File count factor
  const fileCount = repoInfo.contents.length;
  if (fileCount > 50) score += 30;
  else if (fileCount > 20) score += 20;
  else if (fileCount > 10) score += 10;
  
  // Language diversity factor
  const languageCount = Object.keys(repoInfo.languages).length;
  score += Math.min(languageCount * 5, 25);
  
  // Repository size factor
  const sizeKB = repoInfo.basic.size;
  if (sizeKB > 10000) score += 25; // > 10MB
  else if (sizeKB > 1000) score += 15; // > 1MB
  else if (sizeKB > 100) score += 10; // > 100KB
  
  // Activity factor (based on recent commits)
  if (repoInfo.commits.length >= 10) score += 20;
  else if (repoInfo.commits.length >= 5) score += 10;
  
  return Math.min(score, 100); // Cap at 100
};

// Generate quality metrics
const generateQualityMetrics = (repoInfo) => {
  const metrics = {
    documentation: 0,
    maintainability: 0,
    activity: 0,
    community: 0
  };
  
  // Documentation score
  if (repoInfo.readme) metrics.documentation += 40;
  if (repoInfo.basic.hasWiki) metrics.documentation += 20;
  if (repoInfo.basic.license) metrics.documentation += 20;
  if (repoInfo.basic.description) metrics.documentation += 20;
  
  // Maintainability score
  const daysSinceUpdate = Math.floor((Date.now() - new Date(repoInfo.basic.pushedAt)) / (1000 * 60 * 60 * 24));
  if (daysSinceUpdate < 30) metrics.maintainability += 40;
  else if (daysSinceUpdate < 90) metrics.maintainability += 30;
  else if (daysSinceUpdate < 180) metrics.maintainability += 20;
  else metrics.maintainability += 10;
  
  if (repoInfo.commits.length >= 10) metrics.maintainability += 30;
  else if (repoInfo.commits.length >= 5) metrics.maintainability += 20;
  
  if (repoInfo.basic.hasIssues) metrics.maintainability += 30;
  
  // Activity score
  metrics.activity = Math.min(repoInfo.commits.length * 10, 100);
  
  // Community score
  metrics.community += Math.min(repoInfo.basic.stars * 2, 50);
  metrics.community += Math.min(repoInfo.basic.forks * 5, 30);
  metrics.community += Math.min(repoInfo.basic.watchers, 20);
  
  return metrics;
};

// Extract features from README content
const extractFeaturesFromReadme = (readmeContent) => {
  if (!readmeContent) return [];
  
  const features = [];
  const lines = readmeContent.split('\n');
  
  // Look for feature lists (lines starting with -, *, or numbers)
  lines.forEach(line => {
    const trimmedLine = line.trim();
    if (trimmedLine.match(/^[-*•]\s+/) || trimmedLine.match(/^\d+\.\s+/)) {
      const feature = trimmedLine.replace(/^[-*•]\s+/, '').replace(/^\d+\.\s+/, '').trim();
      if (feature.length > 10 && feature.length < 100) {
        features.push(feature);
      }
    }
  });
  
  return features.slice(0, 10); // Limit to 10 features
};

export default {
  getRepositoryInfo,
  analyzeRepository
};
