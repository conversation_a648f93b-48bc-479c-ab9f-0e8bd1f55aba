import { body, param, query, validationResult } from 'express-validator';
import { isValidEmail, isValidPassword, isValidGitHubUrl } from '../utils/helpers.js';
import { USER_ROLES, PROJECT_CATEGORIES, TECH_STACKS } from '../utils/constants.js';

// Handle validation errors
export const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => ({
      field: error.path,
      message: error.msg,
      value: error.value
    }));
    
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errorMessages
    });
  }
  
  next();
};

// User validation rules
export const validateUserRegistration = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Name must be between 2 and 50 characters')
    .matches(/^[a-zA-Z\s]+$/)
    .withMessage('Name can only contain letters and spaces'),
    
  body('email')
    .trim()
    .toLowerCase()
    .isEmail()
    .withMessage('Please provide a valid email address')
    .normalizeEmail(),
    
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, and one number'),
    
  body('role')
    .optional()
    .isIn(Object.values(USER_ROLES))
    .withMessage('Invalid user role'),
    
  handleValidationErrors
];

export const validateUserLogin = [
  body('email')
    .trim()
    .toLowerCase()
    .isEmail()
    .withMessage('Please provide a valid email address')
    .normalizeEmail(),
    
  body('password')
    .notEmpty()
    .withMessage('Password is required'),
    
  handleValidationErrors
];

export const validateUserUpdate = [
  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Name must be between 2 and 50 characters'),
    
  body('bio')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Bio cannot exceed 500 characters'),
    
  body('location')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Location cannot exceed 100 characters'),
    
  body('website')
    .optional()
    .isURL()
    .withMessage('Please provide a valid website URL'),
    
  body('socialLinks.github')
    .optional()
    .isURL()
    .withMessage('Please provide a valid GitHub URL'),
    
  body('socialLinks.linkedin')
    .optional()
    .isURL()
    .withMessage('Please provide a valid LinkedIn URL'),
    
  body('socialLinks.twitter')
    .optional()
    .isURL()
    .withMessage('Please provide a valid Twitter URL'),
    
  body('socialLinks.portfolio')
    .optional()
    .isURL()
    .withMessage('Please provide a valid portfolio URL'),
    
  handleValidationErrors
];

// Project validation rules
export const validateProjectCreation = [
  body('title')
    .trim()
    .isLength({ min: 3, max: 100 })
    .withMessage('Title must be between 3 and 100 characters'),
    
  body('description')
    .trim()
    .isLength({ min: 10, max: 2000 })
    .withMessage('Description must be between 10 and 2000 characters'),
    
  body('category')
    .isIn(Object.values(PROJECT_CATEGORIES))
    .withMessage('Invalid project category'),
    
  body('techStack')
    .isArray({ min: 1 })
    .withMessage('At least one technology must be specified')
    .custom((techStack) => {
      const validTechs = Object.values(TECH_STACKS);
      const invalidTechs = techStack.filter(tech => !validTechs.includes(tech));
      if (invalidTechs.length > 0) {
        throw new Error(`Invalid technologies: ${invalidTechs.join(', ')}`);
      }
      return true;
    }),
    
  body('price')
    .isFloat({ min: 0, max: 10000 })
    .withMessage('Price must be between 0 and 10000'),
    
  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array')
    .custom((tags) => {
      if (tags.length > 10) {
        throw new Error('Maximum 10 tags allowed');
      }
      return true;
    }),
    
  body('githubUrl')
    .optional()
    .custom((url) => {
      if (url && !isValidGitHubUrl(url)) {
        throw new Error('Please provide a valid GitHub repository URL');
      }
      return true;
    }),
    
  body('demoUrl')
    .optional()
    .isURL()
    .withMessage('Please provide a valid demo URL'),
    
  body('features')
    .optional()
    .isArray()
    .withMessage('Features must be an array'),
    
  body('requirements')
    .optional()
    .isArray()
    .withMessage('Requirements must be an array'),
    
  handleValidationErrors
];

export const validateProjectUpdate = [
  body('title')
    .optional()
    .trim()
    .isLength({ min: 3, max: 100 })
    .withMessage('Title must be between 3 and 100 characters'),
    
  body('description')
    .optional()
    .trim()
    .isLength({ min: 10, max: 2000 })
    .withMessage('Description must be between 10 and 2000 characters'),
    
  body('category')
    .optional()
    .isIn(Object.values(PROJECT_CATEGORIES))
    .withMessage('Invalid project category'),
    
  body('techStack')
    .optional()
    .isArray({ min: 1 })
    .withMessage('At least one technology must be specified'),
    
  body('price')
    .optional()
    .isFloat({ min: 0, max: 10000 })
    .withMessage('Price must be between 0 and 10000'),
    
  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array'),
    
  handleValidationErrors
];

// Review validation rules
export const validateReview = [
  body('rating')
    .isInt({ min: 1, max: 5 })
    .withMessage('Rating must be between 1 and 5'),
    
  body('comment')
    .trim()
    .isLength({ min: 10, max: 1000 })
    .withMessage('Comment must be between 10 and 1000 characters'),
    
  handleValidationErrors
];

// Parameter validation
export const validateObjectId = (paramName = 'id') => [
  param(paramName)
    .isMongoId()
    .withMessage(`Invalid ${paramName} format`),
    
  handleValidationErrors
];

// Query validation
export const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
    
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
    
  handleValidationErrors
];

export const validateProjectFilters = [
  query('category')
    .optional()
    .isIn(Object.values(PROJECT_CATEGORIES))
    .withMessage('Invalid category filter'),
    
  query('techStack')
    .optional()
    .custom((value) => {
      if (typeof value === 'string') {
        value = [value];
      }
      if (Array.isArray(value)) {
        const validTechs = Object.values(TECH_STACKS);
        const invalidTechs = value.filter(tech => !validTechs.includes(tech));
        if (invalidTechs.length > 0) {
          throw new Error(`Invalid tech stack filters: ${invalidTechs.join(', ')}`);
        }
      }
      return true;
    }),
    
  query('minPrice')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Minimum price must be a positive number'),
    
  query('maxPrice')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Maximum price must be a positive number'),
    
  query('minRating')
    .optional()
    .isFloat({ min: 0, max: 5 })
    .withMessage('Minimum rating must be between 0 and 5'),
    
  query('sortBy')
    .optional()
    .isIn(['createdAt', 'price', 'rating', 'sales', 'title'])
    .withMessage('Invalid sort field'),
    
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('Sort order must be asc or desc'),
    
  handleValidationErrors
];

// Password validation
export const validatePasswordChange = [
  body('currentPassword')
    .notEmpty()
    .withMessage('Current password is required'),
    
  body('newPassword')
    .isLength({ min: 8 })
    .withMessage('New password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('New password must contain at least one uppercase letter, one lowercase letter, and one number'),
    
  body('confirmPassword')
    .custom((value, { req }) => {
      if (value !== req.body.newPassword) {
        throw new Error('Password confirmation does not match new password');
      }
      return true;
    }),
    
  handleValidationErrors
];
