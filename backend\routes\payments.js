import express from 'express';
import {
  createPaymentIntentForProject,
  confirmPayment,
  getPurchaseHistory,
  getSalesHistory,
  processPayout,
  handleStripeWebhook
} from '../controllers/paymentController.js';
import { protect, isSeller } from '../middleware/auth.js';
import { body } from 'express-validator';
import { handleValidationErrors } from '../middleware/validation.js';

const router = express.Router();

// Webhook route (must be before other middleware)
router.post('/webhook', express.raw({ type: 'application/json' }), handleStripeWebhook);

// Protected routes
router.use(protect);

// Payment intent creation
router.post(
  '/create-intent',
  [
    body('projectId')
      .notEmpty()
      .withMessage('Project ID is required')
      .isMongoId()
      .withMessage('Invalid project ID'),
    body('couponCode')
      .optional()
      .isString()
      .withMessage('Coupon code must be a string'),
    handleValidationErrors
  ],
  createPaymentIntentForProject
);

// Payment confirmation
router.post(
  '/confirm',
  [
    body('transactionId')
      .notEmpty()
      .withMessage('Transaction ID is required'),
    body('paymentIntentId')
      .notEmpty()
      .withMessage('Payment Intent ID is required'),
    handleValidationErrors
  ],
  confirmPayment
);

// Purchase history for buyers
router.get('/purchases', getPurchaseHistory);

// Sales history for sellers
router.get('/sales', isSeller, getSalesHistory);

// Payout processing for sellers
router.post(
  '/payout',
  isSeller,
  [
    body('amount')
      .isFloat({ min: 1 })
      .withMessage('Amount must be at least $1'),
    handleValidationErrors
  ],
  processPayout
);

export default router;
