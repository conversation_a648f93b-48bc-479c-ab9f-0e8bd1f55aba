import User from '../models/User.js';
import { generateToken } from '../utils/helpers.js';
import { asyncHandler } from '../middleware/errorHandler.js';
import { createStripeCustomer, createConnectedAccount } from '../config/stripe.js';
import { USER_ROLES, SUCCESS_MESSAGES, ERROR_MESSAGES } from '../utils/constants.js';
import {
  handleUserSignUp,
  handleUserSignIn,
  handlePasswordReset,
  updateUserPassword,
  deleteSupabaseUser
} from '../config/supabase.js';

// @desc    Register a new user
// @route   POST /api/auth/register
// @access  Public
export const register = asyncHandler(async (req, res) => {
  const { name, email, password, role = USER_ROLES.BUYER } = req.body;

  try {
    // Create user in Supabase and sync with MongoDB
    const { supabaseUser, mongoUser } = await handleUserSignUp(email, password, {
      name,
      role
    });

    // Create Stripe customer for buyers or connected account for sellers
    try {
      if (role === USER_ROLES.BUYER) {
        const stripeCustomer = await createStripeCustomer(email, name, {
          userId: mongoUser._id.toString()
        });
        mongoUser.stripeCustomerId = stripeCustomer.id;
      } else if (role === USER_ROLES.SELLER) {
        const stripeAccount = await createConnectedAccount(email);
        mongoUser.stripeAccountId = stripeAccount.id;
      }

      await mongoUser.save();
    } catch (stripeError) {
      console.error('Stripe account creation error:', stripeError);
      // Continue without Stripe setup - can be done later
    }

    res.status(201).json({
      success: true,
      message: SUCCESS_MESSAGES.USER_CREATED,
      data: {
        user: mongoUser,
        supabaseUser: {
          id: supabaseUser.id,
          email: supabaseUser.email,
          email_confirmed_at: supabaseUser.email_confirmed_at
        }
      }
    });
  } catch (error) {
    console.error('Registration error:', error);

    // Handle specific Supabase errors
    if (error.message.includes('already registered')) {
      return res.status(400).json({
        success: false,
        error: 'User with this email already exists'
      });
    }

    return res.status(400).json({
      success: false,
      error: error.message || 'Registration failed'
    });
  }
});

// @desc    Login user
// @route   POST /api/auth/login
// @access  Public
export const login = asyncHandler(async (req, res) => {
  const { email, password } = req.body;

  try {
    // Sign in with Supabase and sync with MongoDB
    const { supabaseUser, mongoUser, session } = await handleUserSignIn(email, password);

    // Remove sensitive data from response
    const userResponse = { ...mongoUser.toObject() };
    delete userResponse.password;
    delete userResponse.stripeCustomerId;
    delete userResponse.stripeAccountId;

    res.status(200).json({
      success: true,
      message: SUCCESS_MESSAGES.LOGIN_SUCCESS,
      data: {
        user: userResponse,
        session: {
          access_token: session.access_token,
          refresh_token: session.refresh_token,
          expires_at: session.expires_at,
          expires_in: session.expires_in
        },
        supabaseUser: {
          id: supabaseUser.id,
          email: supabaseUser.email,
          last_sign_in_at: supabaseUser.last_sign_in_at
        }
      }
    });
  } catch (error) {
    console.error('Login error:', error);

    // Handle specific Supabase errors
    if (error.message.includes('Invalid login credentials')) {
      return res.status(401).json({
        success: false,
        error: 'Invalid email or password'
      });
    }

    return res.status(401).json({
      success: false,
      error: error.message || 'Login failed'
    });
  }
});

// @desc    Get current logged in user
// @route   GET /api/auth/me
// @access  Private
export const getMe = asyncHandler(async (req, res) => {
  const user = await User.findById(req.user._id)
    .populate('projectsCount')
    .populate('reviewsCount');

  res.status(200).json({
    success: true,
    data: {
      user
    }
  });
});

// @desc    Update user profile
// @route   PUT /api/auth/profile
// @access  Private
export const updateProfile = asyncHandler(async (req, res) => {
  const fieldsToUpdate = {
    name: req.body.name,
    bio: req.body.bio,
    location: req.body.location,
    website: req.body.website,
    socialLinks: req.body.socialLinks
  };

  // Remove undefined fields
  Object.keys(fieldsToUpdate).forEach(key => {
    if (fieldsToUpdate[key] === undefined) {
      delete fieldsToUpdate[key];
    }
  });

  const user = await User.findByIdAndUpdate(
    req.user._id,
    fieldsToUpdate,
    {
      new: true,
      runValidators: true
    }
  );

  res.status(200).json({
    success: true,
    message: 'Profile updated successfully',
    data: {
      user
    }
  });
});

// @desc    Change password
// @route   PUT /api/auth/password
// @access  Private
export const changePassword = asyncHandler(async (req, res) => {
  const { currentPassword, newPassword } = req.body;

  // Get user with password
  const user = await User.findById(req.user._id).select('+password');

  // Check current password
  const isCurrentPasswordCorrect = await user.comparePassword(currentPassword);
  if (!isCurrentPasswordCorrect) {
    return res.status(400).json({
      success: false,
      error: 'Current password is incorrect'
    });
  }

  // Update password
  user.password = newPassword;
  await user.save();

  res.status(200).json({
    success: true,
    message: 'Password changed successfully'
  });
});

// @desc    Logout user
// @route   POST /api/auth/logout
// @access  Private
export const logout = asyncHandler(async (req, res) => {
  // If using cookies, clear the cookie
  res.cookie('token', 'none', {
    expires: new Date(Date.now() + 10 * 1000),
    httpOnly: true
  });

  res.status(200).json({
    success: true,
    message: SUCCESS_MESSAGES.LOGOUT_SUCCESS
  });
});

// @desc    Delete user account
// @route   DELETE /api/auth/account
// @access  Private
export const deleteAccount = asyncHandler(async (req, res) => {
  const { password } = req.body;

  if (!password) {
    return res.status(400).json({
      success: false,
      error: 'Password is required to delete account'
    });
  }

  // Get user with password
  const user = await User.findById(req.user._id).select('+password');

  // Verify password
  const isPasswordCorrect = await user.comparePassword(password);
  if (!isPasswordCorrect) {
    return res.status(400).json({
      success: false,
      error: 'Incorrect password'
    });
  }

  // Soft delete - deactivate account instead of hard delete
  user.isActive = false;
  user.email = `deleted_${Date.now()}_${user.email}`;
  await user.save();

  res.status(200).json({
    success: true,
    message: 'Account deleted successfully'
  });
});

// @desc    Get user stats (for dashboard)
// @route   GET /api/auth/stats
// @access  Private
export const getUserStats = asyncHandler(async (req, res) => {
  const userId = req.user._id;
  const userRole = req.user.role;

  let stats = {};

  if (userRole === USER_ROLES.SELLER) {
    // Get seller-specific stats
    const sellerStats = await User.getSellerStats(userId);
    
    // Get additional project stats
    const Project = (await import('../models/Project.js')).default;
    const projectStats = await Project.aggregate([
      { $match: { uploadedBy: userId } },
      {
        $group: {
          _id: null,
          totalProjects: { $sum: 1 },
          totalViews: { $sum: '$views' },
          totalDownloads: { $sum: '$downloads' },
          averagePrice: { $avg: '$price' }
        }
      }
    ]);

    stats = {
      ...sellerStats,
      projectStats: projectStats[0] || {
        totalProjects: 0,
        totalViews: 0,
        totalDownloads: 0,
        averagePrice: 0
      }
    };
  } else if (userRole === USER_ROLES.BUYER) {
    // Get buyer-specific stats
    const user = await User.findById(userId);
    
    stats = {
      totalPurchases: user.purchaseHistory.length,
      totalSpent: user.purchaseHistory.reduce((sum, purchase) => sum + purchase.amount, 0),
      recentPurchases: user.purchaseHistory
        .sort((a, b) => b.purchasedAt - a.purchasedAt)
        .slice(0, 5)
    };
  }

  res.status(200).json({
    success: true,
    data: {
      stats
    }
  });
});
