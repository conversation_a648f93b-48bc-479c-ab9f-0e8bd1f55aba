import express from 'express';
import {
  analyzeProjectWithAI,
  getProjectSuggestions,
  getProjectRecommendations,
  analyzeGitHubRepository
} from '../controllers/aiController.js';
import { protect, isSeller } from '../middleware/auth.js';
import { validateObjectId } from '../middleware/validation.js';
import { body } from 'express-validator';
import { handleValidationErrors } from '../middleware/validation.js';

const router = express.Router();

// All routes require authentication
router.use(protect);

// AI analysis routes
router.post(
  '/analyze/:projectId',
  validateObjectId('projectId'),
  analyzeProjectWithAI
);

router.get(
  '/suggestions/:projectId',
  validateObjectId('projectId'),
  getProjectSuggestions
);

// Buyer recommendations
router.get('/recommendations', getProjectRecommendations);

// GitHub analysis
router.post(
  '/analyze-github',
  [
    body('githubUrl')
      .notEmpty()
      .withMessage('GitHub URL is required')
      .isURL()
      .withMessage('Please provide a valid URL'),
    handleValidationErrors
  ],
  analyzeGitHubRepository
);

export default router;
