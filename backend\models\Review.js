import mongoose from 'mongoose';
import { REVIEW_RATINGS } from '../utils/constants.js';

const reviewSchema = new mongoose.Schema({
  project: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Project',
    required: [true, 'Project reference is required']
  },
  buyer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Buyer reference is required']
  },
  seller: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Seller reference is required']
  },
  transaction: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Transaction',
    required: [true, 'Transaction reference is required']
  },
  rating: {
    type: Number,
    required: [true, 'Rating is required'],
    min: [REVIEW_RATINGS.MIN, `Rating must be at least ${REVIEW_RATINGS.MIN}`],
    max: [REVIEW_RATINGS.MAX, `Rating cannot exceed ${REVIEW_RATINGS.MAX}`]
  },
  comment: {
    type: String,
    required: [true, 'Review comment is required'],
    trim: true,
    minlength: [10, 'Comment must be at least 10 characters'],
    maxlength: [1000, 'Comment cannot exceed 1000 characters']
  },
  // Detailed ratings (optional)
  detailedRatings: {
    codeQuality: {
      type: Number,
      min: 1,
      max: 5
    },
    documentation: {
      type: Number,
      min: 1,
      max: 5
    },
    easeOfUse: {
      type: Number,
      min: 1,
      max: 5
    },
    valueForMoney: {
      type: Number,
      min: 1,
      max: 5
    },
    sellerSupport: {
      type: Number,
      min: 1,
      max: 5
    }
  },
  // Review status
  isVerified: {
    type: Boolean,
    default: false
  },
  isHelpful: {
    type: Number,
    default: 0
  },
  isReported: {
    type: Boolean,
    default: false
  },
  reportReason: String,
  // Seller response
  sellerResponse: {
    comment: String,
    respondedAt: Date
  },
  // Moderation
  isModerated: {
    type: Boolean,
    default: false
  },
  moderationNotes: String,
  moderatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  moderatedAt: Date
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
reviewSchema.index({ project: 1, buyer: 1 }, { unique: true }); // One review per buyer per project
reviewSchema.index({ project: 1 });
reviewSchema.index({ buyer: 1 });
reviewSchema.index({ seller: 1 });
reviewSchema.index({ rating: -1 });
reviewSchema.index({ createdAt: -1 });
reviewSchema.index({ isVerified: 1 });

// Virtual for helpful votes
reviewSchema.virtual('helpfulVotes', {
  ref: 'ReviewVote',
  localField: '_id',
  foreignField: 'review',
  count: true
});

// Pre-save middleware
reviewSchema.pre('save', function(next) {
  // Mark as verified if buyer has purchased the project
  if (this.transaction && !this.isVerified) {
    this.isVerified = true;
  }
  next();
});

// Post-save middleware to update project rating
reviewSchema.post('save', async function() {
  try {
    const Project = mongoose.model('Project');
    const project = await Project.findById(this.project);
    if (project) {
      await project.updateRating();
    }
  } catch (error) {
    console.error('Error updating project rating:', error);
  }
});

// Post-remove middleware to update project rating
reviewSchema.post('deleteOne', { document: true, query: false }, async function() {
  try {
    const Project = mongoose.model('Project');
    const project = await Project.findById(this.project);
    if (project) {
      await project.updateRating();
    }
  } catch (error) {
    console.error('Error updating project rating after review deletion:', error);
  }
});

// Static method to get reviews for a project
reviewSchema.statics.getProjectReviews = function(projectId, options = {}) {
  const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc' } = options;
  
  const sortOptions = {};
  sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;
  
  const skip = (page - 1) * limit;
  
  return this.find({ project: projectId, isModerated: false })
    .sort(sortOptions)
    .skip(skip)
    .limit(limit)
    .populate('buyer', 'name avatar')
    .populate('sellerResponse.respondedBy', 'name avatar');
};

// Static method to get seller reviews
reviewSchema.statics.getSellerReviews = function(sellerId, options = {}) {
  const { page = 1, limit = 10 } = options;
  const skip = (page - 1) * limit;
  
  return this.find({ seller: sellerId, isModerated: false })
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit)
    .populate('buyer', 'name avatar')
    .populate('project', 'title slug');
};

// Static method to get review statistics
reviewSchema.statics.getReviewStats = async function(projectId) {
  const stats = await this.aggregate([
    { $match: { project: mongoose.Types.ObjectId(projectId), isModerated: false } },
    {
      $group: {
        _id: null,
        totalReviews: { $sum: 1 },
        averageRating: { $avg: '$rating' },
        ratingDistribution: {
          $push: '$rating'
        }
      }
    },
    {
      $project: {
        totalReviews: 1,
        averageRating: { $round: ['$averageRating', 1] },
        ratingCounts: {
          5: { $size: { $filter: { input: '$ratingDistribution', cond: { $eq: ['$$this', 5] } } } },
          4: { $size: { $filter: { input: '$ratingDistribution', cond: { $eq: ['$$this', 4] } } } },
          3: { $size: { $filter: { input: '$ratingDistribution', cond: { $eq: ['$$this', 3] } } } },
          2: { $size: { $filter: { input: '$ratingDistribution', cond: { $eq: ['$$this', 2] } } } },
          1: { $size: { $filter: { input: '$ratingDistribution', cond: { $eq: ['$$this', 1] } } } }
        }
      }
    }
  ]);
  
  return stats[0] || {
    totalReviews: 0,
    averageRating: 0,
    ratingCounts: { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 }
  };
};

// Instance method to mark as helpful
reviewSchema.methods.markAsHelpful = function() {
  return this.updateOne({ $inc: { isHelpful: 1 } });
};

// Instance method to add seller response
reviewSchema.methods.addSellerResponse = function(comment, sellerId) {
  this.sellerResponse = {
    comment,
    respondedBy: sellerId,
    respondedAt: new Date()
  };
  return this.save();
};

export default mongoose.model('Review', reviewSchema);
