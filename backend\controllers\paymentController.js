import Project from '../models/Project.js';
import Transaction from '../models/Transaction.js';
import User from '../models/User.js';
import { asyncHandler } from '../middleware/errorHandler.js';
import { 
  createPaymentIntent, 
  createStripeCustomer, 
  transferToSeller,
  verifyWebhookSignature 
} from '../config/stripe.js';
import { TRANSACTION_STATUS, SUCCESS_MESSAGES } from '../utils/constants.js';

// @desc    Create payment intent for project purchase
// @route   POST /api/payments/create-intent
// @access  Private
export const createPaymentIntentForProject = asyncHandler(async (req, res) => {
  const { projectId, couponCode } = req.body;
  const buyerId = req.user._id;

  // Get project details
  const project = await Project.findById(projectId).populate('uploadedBy');
  
  if (!project) {
    return res.status(404).json({
      success: false,
      error: 'Project not found'
    });
  }

  if (project.status !== 'approved' || !project.isActive) {
    return res.status(400).json({
      success: false,
      error: 'Project is not available for purchase'
    });
  }

  // Check if user already purchased this project
  const existingPurchase = await Transaction.findOne({
    project: projectId,
    buyer: buyerId,
    status: TRANSACTION_STATUS.COMPLETED
  });

  if (existingPurchase) {
    return res.status(400).json({
      success: false,
      error: 'You have already purchased this project'
    });
  }

  // Check if user is trying to buy their own project
  if (project.uploadedBy._id.toString() === buyerId.toString()) {
    return res.status(400).json({
      success: false,
      error: 'You cannot purchase your own project'
    });
  }

  try {
    let finalAmount = project.price;
    let discountAmount = 0;

    // Apply coupon if provided (implement coupon logic here)
    if (couponCode) {
      // TODO: Implement coupon validation and discount calculation
      // For now, we'll skip coupon logic
    }

    // Calculate platform fee (5% platform fee)
    const platformFeePercentage = 5;
    const platformFee = (finalAmount * platformFeePercentage) / 100;

    // Ensure buyer has Stripe customer ID
    let buyer = await User.findById(buyerId);
    if (!buyer.stripeCustomerId) {
      const stripeCustomer = await createStripeCustomer(
        buyer.email, 
        buyer.name, 
        { userId: buyerId.toString() }
      );
      buyer.stripeCustomerId = stripeCustomer.id;
      await buyer.save();
    }

    // Create payment intent
    const paymentIntent = await createPaymentIntent(
      finalAmount,
      'usd',
      {
        projectId: projectId.toString(),
        buyerId: buyerId.toString(),
        sellerId: project.uploadedBy._id.toString(),
        projectTitle: project.title
      }
    );

    // Create transaction record
    const transaction = await Transaction.create({
      stripePaymentIntentId: paymentIntent.id,
      buyer: buyerId,
      seller: project.uploadedBy._id,
      project: projectId,
      amount: finalAmount,
      platformFeePercentage,
      platformFee,
      sellerEarnings: finalAmount - platformFee,
      status: TRANSACTION_STATUS.PENDING,
      metadata: {
        couponCode,
        discountAmount
      }
    });

    res.status(200).json({
      success: true,
      data: {
        clientSecret: paymentIntent.client_secret,
        transactionId: transaction.transactionId,
        amount: finalAmount,
        platformFee,
        sellerEarnings: finalAmount - platformFee,
        project: {
          id: project._id,
          title: project.title,
          seller: project.uploadedBy.name
        }
      }
    });
  } catch (error) {
    console.error('Payment intent creation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create payment intent'
    });
  }
});

// @desc    Confirm payment and complete transaction
// @route   POST /api/payments/confirm
// @access  Private
export const confirmPayment = asyncHandler(async (req, res) => {
  const { transactionId, paymentIntentId } = req.body;

  const transaction = await Transaction.findOne({ 
    transactionId,
    stripePaymentIntentId: paymentIntentId,
    buyer: req.user._id 
  });

  if (!transaction) {
    return res.status(404).json({
      success: false,
      error: 'Transaction not found'
    });
  }

  if (transaction.status === TRANSACTION_STATUS.COMPLETED) {
    return res.status(400).json({
      success: false,
      error: 'Transaction already completed'
    });
  }

  try {
    // Mark transaction as completed
    await transaction.markAsCompleted();

    // Get project and seller details
    const project = await Project.findById(transaction.project);
    const seller = await User.findById(transaction.seller);

    res.status(200).json({
      success: true,
      message: SUCCESS_MESSAGES.PAYMENT_SUCCESS,
      data: {
        transaction: {
          id: transaction.transactionId,
          amount: transaction.amount,
          project: project.title,
          seller: seller.name,
          completedAt: transaction.completedAt
        }
      }
    });
  } catch (error) {
    console.error('Payment confirmation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to confirm payment'
    });
  }
});

// @desc    Get user's purchase history
// @route   GET /api/payments/purchases
// @access  Private
export const getPurchaseHistory = asyncHandler(async (req, res) => {
  const { page = 1, limit = 10 } = req.query;
  const skip = (parseInt(page) - 1) * parseInt(limit);

  const purchases = await Transaction.find({
    buyer: req.user._id,
    status: TRANSACTION_STATUS.COMPLETED
  })
  .populate('project', 'title slug category techStack price files.previewImages')
  .populate('seller', 'name avatar')
  .sort({ completedAt: -1 })
  .skip(skip)
  .limit(parseInt(limit));

  const totalPurchases = await Transaction.countDocuments({
    buyer: req.user._id,
    status: TRANSACTION_STATUS.COMPLETED
  });

  res.status(200).json({
    success: true,
    data: {
      purchases,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(totalPurchases / parseInt(limit)),
        totalItems: totalPurchases,
        itemsPerPage: parseInt(limit)
      }
    }
  });
});

// @desc    Get seller's sales history and earnings
// @route   GET /api/payments/sales
// @access  Private (Seller only)
export const getSalesHistory = asyncHandler(async (req, res) => {
  const { page = 1, limit = 10, startDate, endDate } = req.query;
  const skip = (parseInt(page) - 1) * parseInt(limit);

  let query = {
    seller: req.user._id,
    status: TRANSACTION_STATUS.COMPLETED
  };

  // Add date filter if provided
  if (startDate && endDate) {
    query.completedAt = {
      $gte: new Date(startDate),
      $lte: new Date(endDate)
    };
  }

  const sales = await Transaction.find(query)
    .populate('project', 'title slug category')
    .populate('buyer', 'name avatar')
    .sort({ completedAt: -1 })
    .skip(skip)
    .limit(parseInt(limit));

  const totalSales = await Transaction.countDocuments(query);

  // Get earnings summary
  const earningsSummary = await Transaction.aggregate([
    { $match: { seller: req.user._id, status: TRANSACTION_STATUS.COMPLETED } },
    {
      $group: {
        _id: null,
        totalEarnings: { $sum: '$sellerEarnings' },
        totalSales: { $sum: 1 },
        averageSale: { $avg: '$sellerEarnings' }
      }
    }
  ]);

  res.status(200).json({
    success: true,
    data: {
      sales,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(totalSales / parseInt(limit)),
        totalItems: totalSales,
        itemsPerPage: parseInt(limit)
      },
      summary: earningsSummary[0] || {
        totalEarnings: 0,
        totalSales: 0,
        averageSale: 0
      }
    }
  });
});

// @desc    Process seller payout
// @route   POST /api/payments/payout
// @access  Private (Seller only)
export const processPayout = asyncHandler(async (req, res) => {
  const { amount } = req.body;
  const sellerId = req.user._id;

  // Get seller details
  const seller = await User.findById(sellerId);
  
  if (!seller.stripeAccountId || !seller.stripeOnboardingComplete) {
    return res.status(400).json({
      success: false,
      error: 'Please complete your seller onboarding to receive payouts'
    });
  }

  // Calculate available earnings
  const availableEarnings = await Transaction.aggregate([
    { 
      $match: { 
        seller: sellerId, 
        status: TRANSACTION_STATUS.COMPLETED,
        payoutStatus: 'pending'
      } 
    },
    { $group: { _id: null, total: { $sum: '$sellerEarnings' } } }
  ]);

  const available = availableEarnings[0]?.total || 0;

  if (amount > available) {
    return res.status(400).json({
      success: false,
      error: `Insufficient funds. Available: $${available.toFixed(2)}`
    });
  }

  try {
    // Create transfer to seller's Stripe account
    const transfer = await transferToSeller(
      amount,
      seller.stripeAccountId,
      {
        sellerId: sellerId.toString(),
        payoutDate: new Date().toISOString()
      }
    );

    // Update transactions as paid out
    await Transaction.updateMany(
      {
        seller: sellerId,
        status: TRANSACTION_STATUS.COMPLETED,
        payoutStatus: 'pending'
      },
      {
        payoutStatus: 'paid',
        payoutId: transfer.id,
        payoutDate: new Date(),
        payoutAmount: amount
      }
    );

    res.status(200).json({
      success: true,
      message: 'Payout processed successfully',
      data: {
        transferId: transfer.id,
        amount,
        payoutDate: new Date()
      }
    });
  } catch (error) {
    console.error('Payout processing error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to process payout'
    });
  }
});

// @desc    Handle Stripe webhooks
// @route   POST /api/payments/webhook
// @access  Public (Stripe only)
export const handleStripeWebhook = asyncHandler(async (req, res) => {
  const signature = req.headers['stripe-signature'];

  try {
    const event = verifyWebhookSignature(req.body, signature);

    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentSuccess(event.data.object);
        break;
      case 'payment_intent.payment_failed':
        await handlePaymentFailure(event.data.object);
        break;
      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    res.status(200).json({ received: true });
  } catch (error) {
    console.error('Webhook error:', error);
    res.status(400).json({
      success: false,
      error: 'Webhook verification failed'
    });
  }
});

// Helper function to handle successful payments
const handlePaymentSuccess = async (paymentIntent) => {
  const transaction = await Transaction.findOne({
    stripePaymentIntentId: paymentIntent.id
  });

  if (transaction && transaction.status === TRANSACTION_STATUS.PENDING) {
    await transaction.markAsCompleted();
    console.log(`Payment completed for transaction: ${transaction.transactionId}`);
  }
};

// Helper function to handle failed payments
const handlePaymentFailure = async (paymentIntent) => {
  const transaction = await Transaction.findOne({
    stripePaymentIntentId: paymentIntent.id
  });

  if (transaction && transaction.status === TRANSACTION_STATUS.PENDING) {
    await transaction.markAsFailed(
      paymentIntent.last_payment_error?.message || 'Payment failed',
      paymentIntent.last_payment_error?.code || 'unknown'
    );
    console.log(`Payment failed for transaction: ${transaction.transactionId}`);
  }
};
