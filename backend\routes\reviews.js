import express from 'express';
import {
  createReview,
  getProjectReviews,
  getSellerReviews,
  updateReview,
  deleteReview,
  addSellerResponse,
  markReviewAsHelpful,
  reportReview,
  getMyReviews
} from '../controllers/reviewController.js';
import { protect, isSeller, optionalAuth } from '../middleware/auth.js';
import { validateReview, validateObjectId } from '../middleware/validation.js';
import { body } from 'express-validator';
import { handleValidationErrors } from '../middleware/validation.js';

const router = express.Router();

// Public routes
router.get('/project/:projectId', optionalAuth, validateObjectId('projectId'), getProjectReviews);
router.get('/seller/:sellerId', optionalAuth, validateObjectId('sellerId'), getSellerReviews);

// Protected routes
router.use(protect);

// Create review
router.post('/', validateReview, createReview);

// Get user's own reviews
router.get('/my-reviews', getMyReviews);

// Update review
router.put(
  '/:reviewId',
  validateObjectId('reviewId'),
  [
    body('rating')
      .optional()
      .isInt({ min: 1, max: 5 })
      .withMessage('Rating must be between 1 and 5'),
    body('comment')
      .optional()
      .trim()
      .isLength({ min: 10, max: 1000 })
      .withMessage('Comment must be between 10 and 1000 characters'),
    handleValidationErrors
  ],
  updateReview
);

// Delete review
router.delete('/:reviewId', validateObjectId('reviewId'), deleteReview);

// Mark review as helpful
router.post('/:reviewId/helpful', validateObjectId('reviewId'), markReviewAsHelpful);

// Report review
router.post(
  '/:reviewId/report',
  validateObjectId('reviewId'),
  [
    body('reason')
      .notEmpty()
      .withMessage('Report reason is required')
      .isLength({ min: 10, max: 500 })
      .withMessage('Reason must be between 10 and 500 characters'),
    handleValidationErrors
  ],
  reportReview
);

// Seller response to review
router.post(
  '/:reviewId/response',
  isSeller,
  validateObjectId('reviewId'),
  [
    body('comment')
      .notEmpty()
      .withMessage('Response comment is required')
      .trim()
      .isLength({ min: 10, max: 1000 })
      .withMessage('Response must be between 10 and 1000 characters'),
    handleValidationErrors
  ],
  addSellerResponse
);

export default router;
