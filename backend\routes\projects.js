import express from 'express';
import {
  createProject,
  getProjects,
  getProject,
  updateProject,
  deleteProject,
  getSellerProjects,
  getMyProjects,
  downloadProject
} from '../controllers/projectController.js';
import {
  validateProjectCreation,
  validateProjectUpdate,
  validateObjectId,
  validatePagination,
  validateProjectFilters
} from '../middleware/validation.js';
import {
  protect,
  optionalAuth,
  isSeller,
  canAccessSellerFeatures
} from '../middleware/auth.js';
import {
  uploadProjectFiles,
  handleUploadError,
  validateUploadedFiles
} from '../middleware/upload.js';

const router = express.Router();

// Public routes
router.get('/', optionalAuth, validatePagination, validateProjectFilters, getProjects);
router.get('/seller/:sellerId', optionalAuth, validateObjectId('sellerId'), getSellerProjects);
router.get('/:id', optionalAuth, getProject);

// Protected routes
router.use(protect); // All routes below require authentication

// Seller-only routes
router.post(
  '/',
  isSeller,
  uploadProjectFiles,
  handleUploadError,
  validateUploadedFiles,
  validateProjectCreation,
  createProject
);

router.get('/my/projects', canAccessSellerFeatures, getMyProjects);

router.put(
  '/:id',
  validateObjectId('id'),
  uploadProjectFiles,
  handleUploadError,
  validateProjectUpdate,
  updateProject
);

router.delete('/:id', validateObjectId('id'), deleteProject);

// Download route (for purchased users)
router.get('/:id/download', validateObjectId('id'), downloadProject);

export default router;
